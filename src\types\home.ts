export interface IHome {
    hero: IHero;
    title: string;
    adventure: IAdventure[];
    experience: IExperience[];
    hikingArea: IHikingArea[];
    tailoredAdventure: ITailoredAdventure[];
    review: IReview[];
    testimonial: ITestimonial[];
}

export interface IHero {
    id: number;
    videoUrl: string;
    titles: string[];
    subtitles: string[];
    images: string[];
}

export interface IAdventure {
    id: number;
    title: string;
    images: string[];
    points: string[];
    linkUrl: string;
    linkLabel: string;
}

export interface IExperience {
    id: number;
    title: string;
    subtitle: string;
}

export interface IHikingArea {
    id: number;
    imageFile?: File;
    imagePreview?: string;
    title: string;
    subtitle: string;
    linkUrl: string;
}

export interface ITailoredAdventure {
    id: number;
    title: string;
    subtitle: string;
    buttonText: string;
    buttonLink: string;
}

export interface IReview {
    id: number;
    imageFile?: File;
    imagePreview?: string;
    text: string;
    name: string;
    destination: string;
    date: string;
}


export interface ITestimonial {
    id: number;
    youtubeUrl: string;
    title: string;
    destination: string;
    date: string;
}
