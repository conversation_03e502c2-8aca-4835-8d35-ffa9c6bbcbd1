"use client"

import { <PERSON><PERSON><PERSON>, SetStateAction } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { <PERSON>o<PERSON>ield<PERSON> } from "@/types/package-form"

interface Props {
  formData: SeoFields
  setFormData: Dispatch<SetStateAction<SeoFields>>
}

export function SeoDetailsSection({ formData, setFormData }: Props) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>SEO Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="metaTitle">Title</Label>
            <Input
              id="metaTitle"
              value={formData.metaTitle}
              onChange={e => setFormData(prev => ({ ...prev, metaTitle: e.target.value }))}
              placeholder="Enter Meta Title"
            />
          </div>
          <div>
            <Label htmlFor="metaDescription">Meta Description</Label>
            <Input
              id="metaDescription"
              value={formData.metaDescription}
              onChange={e => setFormData(prev => ({ ...prev, metaDescription: e.target.value }))}
              placeholder="Enter Meta Description"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="metaKeywords">Meta Keywords</Label>
            <Input
              id="metaKeywords"
              value={formData.metaKeywords}
              onChange={e => setFormData(prev => ({ ...prev, metaKeywords: e.target.value }))}
              placeholder="comma, separated, keywords"
            />
          </div>
          <div>
            <Label htmlFor="canonicalUrl">Canonical URL</Label>
            <Input
              id="canonicalUrl"
              value={formData.canonicalUrl}
              onChange={e => setFormData(prev => ({ ...prev, canonicalUrl: e.target.value }))}
              placeholder="https://..."
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
