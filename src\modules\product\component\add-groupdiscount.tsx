import { useState, useEffect, FormEvent } from "react"
import { Plus, Edit, X } from 'lucide-react'
import { GroupDiscountEntry } from "@/types/package-form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

const initialFormState = {
  groupSize: "",
  pricePerPerson: "",
  note: "",
  publish: false,
}

export function AddGroupDiscountForm({
  editingItem,
  onAdd,
  onUpdate,
  onCancelEdit
}: {
  editingItem: GroupDiscountEntry | null,
  onAdd: (data: Omit<GroupDiscountEntry, 'id'>) => void,
  onUpdate: (data: GroupDiscountEntry) => void,
  onCancelEdit: () => void,
}) {
  const [formData, setFormData] = useState(initialFormState)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isEditing = editingItem !== null

  useEffect(() => {
    if (isEditing && editingItem) {
      setFormData({
        groupSize: editingItem.groupSize,
        pricePerPerson: editingItem.pricePerPerson,
        note: editingItem.note || "",
        publish: editingItem.publish || false
      })
    } else {
      setFormData(initialFormState)
    }
  }, [editingItem, isEditing])

  const handleInput = (field: keyof typeof initialFormState, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    try {
      if (isEditing && editingItem) {
        onUpdate({ ...editingItem, ...formData })
      } else {
        onAdd({ ...formData })
      }
      setFormData(initialFormState)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Group Discount' : 'Add Group Discount'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="groupSize">No Of Person</Label>
            <Input
              id="groupSize"
              value={formData.groupSize}
              onChange={e => handleInput("groupSize", e.target.value)}
              required
              placeholder='e.g. "5 Pax", "6+", etc.'
            />
          </div>
          <div>
            <Label htmlFor="pricePerPerson">Price per Person</Label>
            <Input
              id="pricePerPerson"
              value={formData.pricePerPerson}
              onChange={e => handleInput("pricePerPerson", e.target.value)}
              required
              placeholder='e.g. "USD 1400"'
            />
          </div>
          <div>
            <Label htmlFor="note">Note (optional)</Label>
            <Input
              id="note"
              value={formData.note}
              onChange={e => handleInput("note", e.target.value)}
              placeholder='Optional note'
            />
          </div>
          <div>
            <label>
              <input
                type="checkbox"
                checked={formData.publish}
                onChange={e => handleInput("publish", e.target.checked)}
              /> Publish
            </label>
          </div>
          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                <X className="w-4 h-4" /> Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Group Discount'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
