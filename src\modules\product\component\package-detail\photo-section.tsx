'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import { FileUpload } from "../fileupload"

export interface PhotoEntry {
    file: File | null
    caption: string
}

interface PhotoSectionProps {
    title: string
    onTitleChange: (value: string) => void
    photos: PhotoEntry[]
    onAddPhoto: () => void
    onRemovePhoto: (index: number) => void
    onUpdatePhotoFile: (index: number, file: File | null) => void
    onUpdatePhotoCaption: (index: number, caption: string) => void
}

export function PhotoSection({
    title, onTitleChange, photos, onAddPhoto, onRemovePhoto, onUpdatePhotoFile, onUpdatePhotoCaption
}: PhotoSectionProps) {
    return (
        <>
            <Label htmlFor="photo-title">Photo Section Title</Label>
            <Input id="photo-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <div className="flex justify-end mb-4">
                <Button variant="outline" size="sm" onClick={onAddPhoto}>+ Add Photo</Button>
            </div>
            {photos.length === 0 && <p className="text-gray-500 text-center py-8">No photos yet.</p>}
            {photos.map((photo, idx) => (
                <div key={idx} className="relative space-y-4 mb-8 border-b pb-4">
                    <button type="button" onClick={() => onRemovePhoto(idx)} className="absolute top-0 right-0 p-1 text-red-600 hover:bg-red-50 rounded">
                        <Trash2 className="h-4 w-4" />
                    </button>
                    <FileUpload label={`Photo ${idx + 1}`} accept="image/*" showPreview={true} onFileChange={(file) => onUpdatePhotoFile(idx, file)} />
                    <Label htmlFor={`caption-${idx}`}>Caption</Label>
                    <Input id={`caption-${idx}`} value={photo.caption} onChange={e => onUpdatePhotoCaption(idx, e.target.value)} className="mt-1" placeholder="Optional: add a caption" />
                </div>
            ))}
        </>
    )
}