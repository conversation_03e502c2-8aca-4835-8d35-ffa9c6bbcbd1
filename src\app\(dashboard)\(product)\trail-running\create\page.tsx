"use client"
import { CategoryForm, CategoryFormData } from "@/components/category/category-form";
import { useRouter } from "next/navigation";
import React from "react";

export default function CreateTrailRunningCategory() {
  const router = useRouter();
  const handleSubmit = (data: CategoryFormData) => {
    console.log("Trail Running category data:", data);
    router.push("/trail-running");

  };

  return (
    <CategoryForm
      title="Add Trail Running Category"
      onSubmit={handleSubmit}
      cancelPath="/trail-running"
    />
  );
}
