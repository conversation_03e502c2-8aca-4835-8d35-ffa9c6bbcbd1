'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'

interface VideoSectionProps {
    title: string
    onTitleChange: (value: string) => void
    links: string[]
    onAddLink: () => void
    onUpdateLink: (index: number, url: string) => void
    onRemoveLink: (index: number) => void
}

export function VideoSection({ title, onTitleChange, links, onAddLink, onUpdateLink, onRemoveLink }: VideoSectionProps) {
    return (
        <>
            <Label htmlFor="video-title">Video Section Title</Label>
            <Input
                id="video-title"
                value={title}
                onChange={e => onTitleChange(e.target.value)}
                className="mt-1 mb-4"
            />
            <div className="flex justify-end mb-4">
                <Button variant="outline" size="sm" onClick={onAddLink}>
                    + Add YouTube Link
                </Button>
            </div>
            <div className="space-y-6">
                {links.map((link, idx) => (
                    <div key={idx} className="relative">
                        <Label htmlFor={`youtube-link-${idx}`}>YouTube Link #{idx + 1}</Label>
                        <div className="flex items-center space-x-2 mt-1">
                            <Input
                                id={`youtube-link-${idx}`}
                                value={link}
                                onChange={e => onUpdateLink(idx, e.target.value)}
                                placeholder="https://www.youtube.com/watch?v=..."
                            />
                            {/* Show remove button only if there is more than one link */}
                            {links.length > 1 && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onRemoveLink(idx)}
                                    className="text-red-600 hover:bg-red-50 hover:text-red-600 shrink-0"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </>
    )
}