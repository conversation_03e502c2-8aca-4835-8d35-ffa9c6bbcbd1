"use client";
import {
  createContext,
  useContext,
  useState,
  type ReactNode,
} from "react";
import { useRouter } from "next/navigation";
import { useLoginMutation } from "@/modules/auth/mutations/login-mutation";
import User, { LoginResponse } from "@/types/user";

type AuthContextType = {
  user: User | null;
  login: (email: string, password: string) => void;
  logout: () => void;
  isLoading: boolean;
  isAuthenticated: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();
 
  const handleLoginSuccess = (response: LoginResponse) => {
    console.log("Login successful, response:", response);
    const userData: User = response.data; // Type-safe user data
    console.log("Setting user data:", userData);
    setUser(userData);
    router.push("/home/<USER>");
  };
 
  const loginMutation = useLoginMutation(handleLoginSuccess);
  
  const login = (email: string, password: string) => {
    loginMutation.mutate({
      email,
      password,
    });
  };
  
  const logout = () => {
    setUser(null);
    router.push("/login");
  };
  
  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading: loginMutation.isPending,
        isAuthenticated: !!user
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}