import { IActivity } from "@/types/activity";
import { IApiResponse } from "@/types/response";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export function useUpdateActivity() {
    const queryClient = useQueryClient();
    return useMutation<IApiResponse<IActivity>, Error, IActivity>({
        mutationFn: (data) =>
            fetch(`/api/activity/${data.id}`, {
                method: "PUT",
                mode: "cors",
                credentials: "include",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            }).then((res) => {
                if (!res.ok) {
                    throw new Error(res.statusText);
                }
                return res.json();
            }),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["activity"] });
            toast.success("Activity Updated Sucessfully");
        },
        onError: () => {
            toast.error("Error Updating Activity");
        }
    });
}