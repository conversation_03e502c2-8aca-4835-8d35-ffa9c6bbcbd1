export interface PackageFormData {
  name: string;
  slug: string;
  regionId: string;
  activityId: string;
  accomodation: string;
  distance: string;
  type: string;
  duration: string;
  altitude: string;
  meals: string;
  groupSize: string;
  price: string;
  discountPrice: string;
  bestSeason: string;
  transport: string;
  activityPerDay: string;
  grade: string;
  bookingLink: string;
  overviewDescription: string;
  
  thumbnail: string;
  mainImage: string;
  mainImageAlt: string;
  pdfBrochure: string;
  
  published: boolean;
  tripOftheMonth: boolean;
  popularTour: boolean;
  shortTrek: boolean;
}

export interface PackageEntry {
  id: number;
  name: string;
  slug: string;
  regionId: string;
  activityId: string;
  published: boolean;
}

export interface ItineraryItem {
  id: number;
  packageId: number;
  day: string;
  title: string;
  details: string;
  image?: string;
  imageFile?: File;
  heading: string;
  trekDistance: string;
  flightHours: string;
  drivingHour: string;
  highestAltitude: string;
  trekDuration: string;
}

export interface EquipmentEntry {
  id: number;
  packageId: number;
  title: string;
  description: string;
  head: string;
  face: string;
  body: string;
}

export interface FAQEntry {
  id: number;
  packageId: number;
  question: string;
  answer: string;
  publish?: boolean;
}

export interface CostDateEntry {
  id: number;
  packageId: number;
  days: string;
  startDate: string;  
  endDate: string;
  price: string;
  discountPrice: string;
  tripStatus: string;
  publish: boolean;
  upcoming: boolean;
}

export interface GroupDiscountEntry {
  id: number;
  packageId: number;
  groupSize: string;           
  pricePerPerson: string;     
  note?: string;              
  publish?: boolean;
}

export interface ReviewEntry {
  id: number;
  packageId: number;
  name: string;
  email: string;
  rating: string;
  comment: string;
  publish?: boolean;
  image?: string;
  imageFile?: File;
}

export interface SeoFields {
  id?: number;
  packageId: number;
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
  canonicalUrl: string;
}

export interface PhotoEntry {
  id?: number;
  packageId: number;
  file: File | null;
  caption: string;
  url?: string; 
}

export interface InfoEntry {
  id?: number;
  packageId: number;
  title: string;
  body: string;
  note: string;
}

export interface PackageContentData {
  id?: number;
  packageId: number;
  
  highlightsTitle: string;
  highlightsBody: string;
  
  descriptionTitle: string;
  descriptionBody: string;
  
  shortItineraryTitle: string;
  shortItineraryItems: string[];
  
  photoTitle: string;
  photos: PhotoEntry[];
  
  videoTitle: string;
  youtubeLinks: string[];
  
  includesTitle: string;
  includesBody: string;
  
  excludesTitle: string;
  excludesBody: string;
  
  mapTitle: string;
  mapFile?: File;
  mapUrl?: string; 
  
  tripInfoTitle: string;
  tripInfos: InfoEntry[];
}

export interface PackageData {
  id?: number;
  formData: PackageFormData;
  contentData?: PackageContentData;
  itineraryItems?: ItineraryItem[];
  equipmentEntries?: EquipmentEntry[];
  faqEntries?: FAQEntry[];
  costDateEntries?: CostDateEntry[];
  groupDiscountEntries?: GroupDiscountEntry[];
  reviewEntries?: ReviewEntry[];
  seo?: SeoFields;
  schema?: string;
}



export interface PackageApiResponse {
  success: boolean;
  data?: PackageData;
  message?: string;
}

export interface PackageListApiResponse {
  success: boolean;
  data?: PackageEntry[];
  message?: string;
}

export interface PackageFormErrors {
  name?: string;
  slug?: string;
  regionId?: string;
  activityId?: string;
  price?: string;
  discountPrice?: string;
  duration?: string;
  altitude?: string;
}

export interface FileUploadProps {
  label: string;
  accept: string;
  onFileChange: (file: File | null) => void;
}

export type PackageTabType = 'details' | 'itinerary' | 'equipment' | 'cost' | 'discount' | 'faq' | 'reviews' | 'seo' | 'content';