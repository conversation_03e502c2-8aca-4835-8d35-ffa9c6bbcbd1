'use client'

import { useState, useEffect, FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit, X } from 'lucide-react'
import { ReviewEntry } from "@/types/package-form"
import { FileUpload } from "./fileupload"

const initialFormState = {
    name: "",
    email: "",
    rating: "",
    comment: "",
    publish: false,
}

export function AddReviewForm({
    editingItem,
    onAdd,
    onUpdate,
    onCancelEdit
}: {
    editingItem: ReviewEntry | null,
    onAdd: (data: Omit<ReviewEntry, 'id'>) => void,
    onUpdate: (data: ReviewEntry) => void,
    onCancelEdit: () => void,
}) {
    const [formData, setFormData] = useState(initialFormState)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [imageFile, setImageFile] = useState<File | null>(null)

    const isEditing = editingItem !== null

    useEffect(() => {
        if (isEditing && editingItem) {
            setFormData({
                name: editingItem.name,
                email: editingItem.email,
                rating: editingItem.rating,
                comment: editingItem.comment,
                publish: editingItem.publish ?? false,
            })
            setImageFile(null)
        } else {
            setFormData(initialFormState)
            setImageFile(null)
        }
    }, [editingItem, isEditing])

    const handleInput = (field: keyof typeof initialFormState, value: string | boolean) => {
        setFormData(prev => ({ ...prev, [field]: value }))
    }

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)
        try {
            if (isEditing && editingItem) {
                onUpdate({ ...editingItem, ...formData, imageFile: imageFile || undefined })
            } else {
                onAdd({
                    ...formData,
                    imageFile: imageFile || undefined,
                })
            }
            setFormData(initialFormState)
            setImageFile(null)
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
                    {isEditing ? 'Edit Review' : 'Add Review'}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">

                    <div>
                        <Label htmlFor="name">Name</Label>
                        <Input
                            id="name"
                            value={formData.name}
                            onChange={e => handleInput('name', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            value={formData.email}
                            onChange={e => handleInput('email', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="rating">Rating</Label>
                        <Input
                            id="rating"
                            value={formData.rating}
                            onChange={e => handleInput('rating', e.target.value)}
                            placeholder="e.g. 5"
                            required
                        />
                    </div>

                    <div>
                        <Label htmlFor="comment">Comment</Label>
                        <Input
                            id="comment"
                            value={formData.comment}
                            onChange={e => handleInput('comment', e.target.value)}
                            required
                        />
                    </div>

                    <div>
                        <FileUpload
                            label="Review Image"
                            accept="image/*"
                            onFileChange={(file) => setImageFile(file)}
                            showPreview={true}
                            previewSrc={isEditing && editingItem?.image ? editingItem.image : undefined}
                            previewAlt="Review image"
                        />
                    </div>

                    <div>
                        <label>
                            <input
                                type="checkbox"
                                checked={formData.publish}
                                onChange={e => handleInput('publish', e.target.checked)}
                            /> Publish
                        </label>
                    </div>

                    <div className="flex justify-end gap-2 pt-4 border-t">
                        {isEditing && (
                            <Button type="button" variant="outline" onClick={onCancelEdit} disabled={isSubmitting}>
                                <X className="w-4 h-4" /> Cancel
                            </Button>
                        )}
                        <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? 'Saving...' : isEditing ? 'Save Changes' : 'Add Review'}
                        </Button>
                    </div>

                </form>
            </CardContent>
        </Card>
    )
}
