"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Mountain } from "lucide-react";
import { 
  ActivityCategory, 
  ACTIVITY_CATEGORIES, 
  PackageEntry, 
  CategoryPackageListProps 
} from "@/types/package-form";

const getSamplePackagesForCategory = (category: ActivityCategory): PackageEntry[] => {
  const sampleData: Record<ActivityCategory, PackageEntry[]> = {
    'trekpackage': [
      {
        id: 1,
        name: "Khopra Ridge Trek",
        destination: "Nepal", 
        region: "Annapurna Region",
        activity: "trekpackage",
        slug: "khopra-ridge-trek",
        published: true,
      },
      {
        id: 2,
        name: "Everest Base Camp Trek",
        destination: "Nepal",
        region: "Everest Region", 
        activity: "trekpackage",
        slug: "everest-base-camp-trek",
        published: true,
      },
    ],
    'trail-runningpackage': [
      {
        id: 3,
        name: "Annapurna Trail Run",
        destination: "Nepal",
        region: "Annapurna Region",
        activity: "trail-runningpackage", 
        slug: "annapurna-trail-run",
        published: true,
      },
    ],
    'peak-climbingpackage': [
      {
        id: 4,
        name: "Island Peak Climbing",
        destination: "Nepal",
        region: "Everest Region",
        activity: "peak-climbingpackage",
        slug: "island-peak-climbing", 
        published: false,
      },
    ],
    'fastpackingpackage': [
      {
        id: 5,
        name: "Langtang Fast Pack",
        destination: "Nepal", 
        region: "Langtang Region",
        activity: "fastpackingpackage",
        slug: "langtang-fast-pack",
        published: true,
      },
    ],
  };
  
  return sampleData[category] || [];
};

export default function CategoryPackageList({ 
  category, 
  packages, 
  onDelete 
}: CategoryPackageListProps) {
  const [packs, setPacks] = useState<PackageEntry[]>([]);
  const [loading, setLoading] = useState(true);

  const categoryInfo = ACTIVITY_CATEGORIES[category];

  useEffect(() => {
    if (packages) {
      setPacks(packages.filter(pkg => pkg.activity === category));
    } else {
      setPacks(getSamplePackagesForCategory(category));
    }
    setLoading(false);
  }, [category, packages]);

  const handleDelete = (id: number, name: string) => {
    if (confirm(`Are you sure you want to delete "${name}"?`)) {
      setPacks((prev) => prev.filter((p) => p.id !== id));
      if (onDelete) {
        onDelete(id);
      }
    }
  };

  if (loading) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">Loading {categoryInfo.label.toLowerCase()} packages...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">All {categoryInfo.label} Packages</h1>
        <Link href={`/${category}/create`}>
          <Button className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Add {categoryInfo.label}
          </Button>
        </Link>
      </div>

      {/* Packages Table */}
      <div className="bg-white rounded-lg shadow overflow-x-auto">
        {packs.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Mountain className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No {categoryInfo.label.toLowerCase()} packages yet</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first {categoryInfo.label.toLowerCase()} package.</p>
            <Link href={`/${category}/create`}>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create First Package
              </Button>
            </Link>
          </div>
        ) : (
          <table className="min-w-full text-left">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">SN</th>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Package Name</th>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Destination</th>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Region</th>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Slug</th>
                <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Status</th>
                <th className="px-6 py-3"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {packs.map((pkg, i) => (
                <tr key={pkg.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm">{i + 1}</td>
                  <td className="px-6 py-4">
                    <div>
                      <div className="font-medium text-gray-900">{pkg.name}</div>
                      <div className="text-sm text-gray-500">ID: {pkg.id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">{pkg.destination}</td>
                  <td className="px-6 py-4 text-sm text-gray-900">{pkg.region}</td>
                  <td className="px-6 py-4 text-sm text-gray-500 font-mono">{pkg.slug}</td>
                  <td className="px-6 py-4">
                    <Badge variant={pkg.published ? "default" : "secondary"}>
                      {pkg.published ? "Published" : "Draft"}
                    </Badge>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <Link href={`/${category}/edit/${pkg.id}`}>
                      <Button variant="outline" size="sm" className="inline-flex items-center gap-1">
                        <Edit className="w-3 h-3" />
                        Edit
                      </Button>
                    </Link>
                    <Button 
                      variant="destructive" 
                      size="sm" 
                      onClick={() => handleDelete(pkg.id, pkg.name)}
                      className="inline-flex items-center gap-1"
                    >
                      <Trash2 className="w-3 h-3" />
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}