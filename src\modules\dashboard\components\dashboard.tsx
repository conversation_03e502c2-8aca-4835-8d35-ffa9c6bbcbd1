import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Eye, Menu, User } from "lucide-react";

export default function Dashboard() {
  const statsCards = [
    { title: "Packages", count: 58, color: "bg-green-500", link: "More info" },
    { title: "Destinations", count: 3, color: "bg-purple-600", link: "More info" },
    { title: "Blogs", count: 58, color: "bg-red-500", link: "More info" },
    { title: "Subscribers", count: 336, color: "bg-orange-500", link: "More info" },
    { title: "Contact us and Enquiry", count: 272, color: "bg-indigo-700", link: "More info" },
    { title: "Bookings", count: 345, color: "bg-blue-600", link: "More info" },
    { title: "Sliders", count: 4, color: "bg-gray-600", link: "More info" },
  ];

  const enquiries = [
    { id: 1, name: "<PERSON> Spady", email: "<EMAIL>" },
    { id: 2, name: "Hazel Chapman", email: "<EMAIL>" },
    { id: 3, name: "DINESH FERNANDO", email: "<EMAIL>" },
    { id: 4, name: "DINESH FERNANDO", email: "<EMAIL>" },
  ];

  const bookings = [
    { id: 1, name: "MD Hussain Al Shajnush", package: "" },
    { id: 2, name: "Jean-Pierre Ranger", package: "Khopra Ridge Trek" },
    { id: 3, name: "Zoe rawson", package: "Mardi Himal Trek" },
    { id: 4, name: "SATYA DEV", package: "" },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon">
            <Menu className="h-6 w-6" />
          </Button>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <User className="h-4 w-4" />
          <span>North Nepal Admin</span>
        </div>
      </div>

      {/* Stats Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsCards.map((card, index) => (
          <Card key={index} className={`${card.color} text-white`}>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{card.count}</div>
              <div className="text-sm font-medium mb-2">{card.title}</div>
              <div className="text-xs opacity-90 flex items-center">
                {card.link}
                <span className="ml-1">→</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Data Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Message and Enquiry Table */}
        <Card>
          <CardHeader>
            <CardTitle>Message and Enquiry</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>S.N.</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>View</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {enquiries.map((enquiry, index) => (
                  <TableRow key={enquiry.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{enquiry.name}</TableCell>
                    <TableCell>{enquiry.email}</TableCell>
                    <TableCell>
                      <Button size="sm" className="bg-green-500 hover:bg-green-600">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Booking Table */}
        <Card>
          <CardHeader>
            <CardTitle>Booking</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>S.N.</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Package Name</TableHead>
                  <TableHead>View</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {bookings.map((booking, index) => (
                  <TableRow key={booking.id}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>{booking.name}</TableCell>
                    <TableCell>{booking.package || "-"}</TableCell>
                    <TableCell>
                      <Button size="sm" className="bg-green-500 hover:bg-green-600">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
