"use client";

import React, { useState, ChangeEvent, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';

interface HeroEntry {
  id: number;
  videoUrl: string;
  titles: string[];
  subtitles: string[];
  images: string[];
}

// Temporary demo data; replace with real fetch in production
const demoHeroes: HeroEntry[] = [
  {
    id: 1,
    videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
    titles: ['Welcome to Our Site'],
    subtitles: ['Discover amazing content that engages your audience effectively.'],
    images: ['/images/random.jpeg', '/images/random.jpeg'],
  },
  {
    id: 2,
    videoUrl: 'https://player.vimeo.com/video/76979871',
    titles: ['Our Latest Update'],
    subtitles: ['Stay tuned for more exciting news and features coming your way soon!'],
    images: ['/images/random.jpeg'],
  },
];

const EditHeroPage: React.FC = () => {
  const router = useRouter();
  const { id } = useParams(); 
  const heroId = Array.isArray(id) ? parseInt(id[0], 10) : parseInt(id || '', 10);
  const hero = demoHeroes.find((h) => h.id === heroId);

  const [videoUrl, setVideoUrl] = useState('');
  const [titles, setTitles] = useState<string[]>(['']);
  const [subtitles, setSubtitles] = useState<string[]>(['']);
  const [images, setImages] = useState<string[]>([]);

  useEffect(() => {
    if (hero) {
      setVideoUrl(hero.videoUrl);
      setTitles(hero.titles);
      setSubtitles(hero.subtitles);
      setImages(hero.images);
    }
  }, [hero]);

  const handleVideoUrlChange = (e: ChangeEvent<HTMLInputElement>) => {
    setVideoUrl(e.target.value);
  };

  const handleTitleChange = (idx: number, value: string) => {
    const copy = [...titles]; copy[idx] = value; setTitles(copy);
  };
  const addTitle = () => setTitles([...titles, '']);
  const removeTitle = (idx: number) => setTitles(titles.filter((_, i) => i !== idx));

  const handleSubtitleChange = (idx: number, value: string) => {
    const copy = [...subtitles]; copy[idx] = value; setSubtitles(copy);
  };
  const addSubtitle = () => setSubtitles([...subtitles, '']);
  const removeSubtitle = (idx: number) => setSubtitles(subtitles.filter((_, i) => i !== idx));

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const urls = Array.from(e.target.files).map((f) => URL.createObjectURL(f));
    setImages([...images, ...urls]);
  };
  const removeImage = (idx: number) => setImages(images.filter((_, i) => i !== idx));

  const handleSave = () => {
    // TODO: call API to save changes
    console.log('Saving hero', { heroId, videoUrl, titles, subtitles, images });
    router.push('/hero');
  };

  if (!hero) {
    return <div className="p-6">Hero not found.</div>;
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Edit Hero #{heroId}</h2>

      {/* Video URL */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Video URL</label>
        <input
          type="text"
          value={videoUrl}
          onChange={handleVideoUrlChange}
          className="w-full border rounded px-3 py-2"
        />
      </div>

      {/* Titles */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Titles</label>
        {titles.map((t, idx) => (
          <div key={idx} className="flex items-center mb-2">
            <input
              type="text"
              value={t}
              onChange={(e) => handleTitleChange(idx, e.target.value)}
              className="flex-1 border rounded px-3 py-2"
            />
            {titles.length > 1 && (
              <button onClick={() => removeTitle(idx)} className="ml-2 text-red-600">✕</button>
            )}
          </div>
        ))}
        <button onClick={addTitle} className="mt-2 px-3 py-1 bg-blue-600 text-white rounded">+ Title</button>
      </div>

      {/* Subtitles */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Subtitles</label>
        {subtitles.map((s, idx) => (
          <div key={idx} className="flex items-center mb-2">
            <input
              type="text"
              value={s}
              onChange={(e) => handleSubtitleChange(idx, e.target.value)}
              className="flex-1 border rounded px-3 py-2"
            />
            {subtitles.length > 1 && (
              <button onClick={() => removeSubtitle(idx)} className="ml-2 text-red-600">✕</button>
            )}
          </div>
        ))}
        <button onClick={addSubtitle} className="mt-2 px-3 py-1 bg-blue-600 text-white rounded">+ Subtitle</button>
      </div>

      {/* Images */}
      <div className="mb-6">
        <label className="block font-medium mb-1">Images</label>
        <input type="file" accept="image/*" multiple onChange={handleImageUpload} />
        <div className="mt-3 grid grid-cols-3 gap-3">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image src={src} alt={`img-${idx}`} width={80} height={80} className="object-cover rounded" />
              <button onClick={() => removeImage(idx)} className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1">✕</button>
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button onClick={handleSave} className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">Save Changes</button>
        <Link href="/home/<USER>">
          <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">Cancel</button>
        </Link>
      </div>
    </div>
  );
};

export default EditHeroPage;
