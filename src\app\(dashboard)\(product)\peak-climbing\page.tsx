"use client"

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { CategoriesList, Category } from "@/components/category/category-list";

export default function PeakClimbingListPage() {
  const router = useRouter();
  const [treks, setTreks] = useState<Category[]>([
    { id: 1, name: "Annapurna Base Camp", slug: "abc", published: true },
    { id: 2, name: "Everest Base Camp", slug: "ebc", published: true },
  ]);

  return (
    <CategoriesList
      title="All Peak Climbing"
      categories={treks}
      createUrl="/peak-climbing/create"
      onEdit={(id) => router.push(`/peak-climbing/edit/${id}`)}
      onDelete={(id) => setTreks((t) => t.filter((x) => x.id !== id))}
    />
  );
}
