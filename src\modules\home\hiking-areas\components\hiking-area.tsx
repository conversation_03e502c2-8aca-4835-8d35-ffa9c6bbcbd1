"use client";

import React, { useState, useCallback, ChangeEvent } from 'react';
import {
    Dialog,
    DialogTrigger,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

// Data shape
interface HikingArea {
    id: number;
    imageFile?: File;
    imagePreview?: string;
    title: string;
    subtitle: string;
    linkUrl: string;
}

// Initial demo entries
const initialAreas: HikingArea[] = [];

// Dialog for main heading
const SectionHeadingDialog = React.memo<{
    heading: string;
    subheading: string;
    onSave: (h: string, s: string) => void;
}>(function SectionHeadingDialog({ heading, subheading, onSave }) {
    const [open, setOpen] = useState(false);
    const [tempHeading, setTempHeading] = useState(heading);
    const [tempSub, setTempSub] = useState(subheading);
    const handleOpen = () => {
        setTempHeading(heading);
        setTempSub(subheading);
        setOpen(true);
    };
    const handleSave = () => {
        onSave(tempHeading, tempSub);
        setOpen(false);
    };
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button onClick={handleOpen}>Edit Heading</Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Edit Main Heading</DialogTitle>
                    <DialogDescription>Update the page heading and subheading.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div>
                        <label>Heading</label>
                        <input
                            type="text"
                            value={tempHeading}
                            onChange={(e) => setTempHeading(e.target.value)}
                            className="w-full border rounded p-2"
                        />
                    </div>
                    <div>
                        <label>Subheading</label>
                        <textarea
                            value={tempSub}
                            onChange={(e) => setTempSub(e.target.value)}
                            className="w-full border rounded p-2"
                            rows={2}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
                    <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={handleSave}>Save</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
});
SectionHeadingDialog.displayName = 'SectionHeadingDialog';

const AreaDialog = React.memo<{
    area: HikingArea | null;
    onSave: (area: HikingArea | null, file?: File, title?: string, subtitle?: string, link?: string) => void;
}>(function AreaDialog({ area, onSave }) {
    const [open, setOpen] = useState(false);
    const [file, setFile] = useState<File | undefined>(area?.imageFile);
    const [preview, setPreview] = useState(area?.imagePreview);
    const [title, setTitle] = useState(area?.title || '');
    const [subtitle, setSubtitle] = useState(area?.subtitle || '');
    const [linkUrl, setLinkUrl] = useState(area?.linkUrl || '');

    const handleOpen = () => {
        setFile(area?.imageFile);
        setPreview(area?.imagePreview);
        setTitle(area?.title || '');
        setSubtitle(area?.subtitle || '');
        setLinkUrl(area?.linkUrl || '');
        setOpen(true);
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const f = e.target.files?.[0];
        if (f) {
            setFile(f);
            setPreview(URL.createObjectURL(f));
        }
    };

    const handleSave = () => {
        onSave(area,
            file,
            title,
            subtitle,
            linkUrl
        );
        setOpen(false);
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant={area ? 'ghost' : 'outline'} onClick={handleOpen}>
                    {area ? 'Edit' : '+ Add Area'}
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{area ? `Edit ${area.title}` : 'Add Hiking Area'}</DialogTitle>
                    <DialogDescription>Fill in the area details.</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                    <div>
                        <label>Upload Image</label>
                        <input type="file" accept="image/*" onChange={handleFileChange} />
                        {preview && <Image
                            src={preview}
                            alt="preview"
                            width={100}
                            height={100}
                            className="mt-2 h-20 object-cover"
                        />}
                    </div>
                    <div>
                        <label>Title</label>
                        <input
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            className="w-full border rounded p-2"
                        />
                    </div>
                    <div>
                        <label>Subtitle</label>
                        <textarea
                            value={subtitle}
                            onChange={(e) => setSubtitle(e.target.value)}
                            className="w-full border rounded p-2"
                            rows={2}
                        />
                    </div>
                    <div>
                        <label>Link URL</label>
                        <input
                            type="text"
                            value={linkUrl}
                            onChange={(e) => setLinkUrl(e.target.value)}
                            className="w-full border rounded p-2"
                        />
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
                    <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={handleSave}>Save</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
});
AreaDialog.displayName = 'AreaDialog';

const HikingAreasPage: React.FC = () => {
    const [heading, setHeading] = useState('Explore our Hiking Areas – Nepal');
    const [subheading, setSubheading] = useState('Discover top trekking routes across the Himalayas.');
    const [areas, setAreas] = useState<HikingArea[]>(initialAreas);

    const saveHeading = useCallback((h: string, s: string) => {
        setHeading(h);
        setSubheading(s);
    }, []);

    const saveArea = useCallback((area: HikingArea | null, file?: File, title?: string, subtitle?: string, link?: string) => {
        setAreas(prev => {
            if (area) {
                return prev.map(a => a.id === area.id
                    ? {
                        ...a,
                        imageFile: file,
                        imagePreview: file ? URL.createObjectURL(file) : a.imagePreview,
                        title: title || a.title,
                        subtitle: subtitle || a.subtitle,
                        linkUrl: link || a.linkUrl
                    }
                    : a
                );
            } else {
                const nextId = prev.length ? Math.max(...prev.map(a => a.id)) + 1 : 1;
                return [
                    ...prev,
                    {
                        id: nextId,
                        imageFile: file,
                        imagePreview: file ? URL.createObjectURL(file) : undefined,
                        title: title || '',
                        subtitle: subtitle || '',
                        linkUrl: link || ''
                    }
                ];
            }
        });
    }, []);

    const deleteArea = useCallback((id: number) => {
        setAreas(prev => prev.filter(a => a.id !== id));
    }, []);

    return (
        <div className="p-6 bg-gray-100 space-y-6">
            {/* Main heading */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold">{heading}</h1>
                    <p className="text-gray-600">{subheading}</p>
                </div>
                <SectionHeadingDialog heading={heading} subheading={subheading} onSave={saveHeading} />
            </div>

            {/* Table header */}
            <div className="flex justify-between items-center">
                <h2 className="text-2xl font-semibold">Hiking Areas</h2>
                <AreaDialog area={null} onSave={saveArea} />
            </div>

            {/* Table */}
            <div className="bg-white rounded-lg shadow overflow-x-auto">
                <table className="min-w-full">
                    <thead className="bg-gray-50 border-b">
                        <tr>
                            <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">Image</th>
                            <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">Title</th>
                            <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">Subtitle</th>
                            <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">Link</th>
                            <th className="px-6 py-3"></th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {areas.map(area => (
                            <tr key={area.id}>
                                <td className="px-6 py-4">
                                    {area.imagePreview && <Image
                                        src={area.imagePreview}
                                        alt={area.title}
                                        width={100}
                                        height={100}
                                        className="h-16 w-24 object-cover rounded"
                                    />}
                                </td>
                                <td className="px-6 py-4 text-sm text-gray-700">{area.title}</td>
                                <td className="px-6 py-4 text-sm text-gray-700">{area.subtitle}</td>
                                <td className="px-6 py-4 text-sm">
                                    <a href={area.linkUrl} className="text-blue-600 hover:underline">{area.linkUrl}</a>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap space-x-2">
                                    <AreaDialog area={area} onSave={saveArea} />
                                    <Button variant="destructive" size="sm" onClick={() => deleteArea(area.id)}>Delete</Button>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default HikingAreasPage;
