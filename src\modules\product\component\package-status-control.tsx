'use client'

import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { PackageStatusControlsType as StatusControlsType } from "@/types/package-form"

interface PackageStatusControlsProps {
  statusControls: StatusControlsType
  onStatusChange: (statusControls: StatusControlsType) => void
}

export function PackageStatusControls({ statusControls, onStatusChange }: PackageStatusControlsProps) {
  
  // Handle checkbox changes
  const handleCheckboxChange = (field: keyof StatusControlsType, checked: boolean) => {
    onStatusChange({
      ...statusControls,
      [field]: checked
    })
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex gap-8 flex-wrap">
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="published" 
              checked={statusControls.published}
              onCheckedChange={(checked) => handleCheckboxChange('published', !!checked)}
            />
            <Label htmlFor="published" className="cursor-pointer">
              Published
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="trip-month" 
              checked={statusControls.tripOfTheMonth}
              onCheckedChange={(checked) => handleCheckboxChange('tripOfTheMonth', !!checked)}
            />
            <Label htmlFor="trip-month" className="cursor-pointer">
              Trip of the month
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="popular-tours" 
              checked={statusControls.popularTours}
              onCheckedChange={(checked) => handleCheckboxChange('popularTours', !!checked)}
            />
            <Label htmlFor="popular-tours" className="cursor-pointer">
              Popular Tours
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="short-trek" 
              checked={statusControls.shortTrek}
              onCheckedChange={(checked) => handleCheckboxChange('shortTrek', !!checked)}
            />
            <Label htmlFor="short-trek" className="cursor-pointer">
              Short Trek
            </Label>
          </div>
        </div>
        
        {/* Optional: Show current status summary */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Status: {statusControls.published ? 'Published' : 'Draft'}
            {statusControls.tripOfTheMonth && ' • Trip of the Month'}
            {statusControls.popularTours && ' • Popular Tour'}
            {statusControls.shortTrek && ' • Short Trek'}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

// 'use client'

// import { Checkbox } from "@/components/ui/checkbox"
// import { Label } from "@/components/ui/label"
// import { Card, CardContent } from "@/components/ui/card"

// export function PackageStatusControls() {
//   return (
//     <Card>
//       <CardContent className="pt-6">
//         <div className="flex gap-8">
//           <div className="flex items-center space-x-2">
//             <Checkbox id="published" defaultChecked />
//             <Label htmlFor="published">Published</Label>
//           </div>
//           <div className="flex items-center space-x-2">
//             <Checkbox id="trip-month" defaultChecked />
//             <Label htmlFor="trip-month">Trip of the month</Label>
//           </div>
//           <div className="flex items-center space-x-2">
//             <Checkbox id="popular-tours" />
//             <Label htmlFor="popular-tours">Popular Tours</Label>
//           </div>
//           <div className="flex items-center space-x-2">
//             <Checkbox id="short-trek" defaultChecked />
//             <Label htmlFor="short-trek">Short Trek</Label>
//           </div>
//         </div>
//       </CardContent>
//     </Card>
//   )
// }
