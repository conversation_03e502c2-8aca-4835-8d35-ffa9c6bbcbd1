"use client";

import React, { useState, useEffect, ChangeEvent } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';

interface DiscoverEntry {
  id: number;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
}

// Demo entries; replace with API call
const demoEntries: DiscoverEntry[] = [
  {
    id: 1,
    title: 'Explore the Mountains',
    images: ['/images/mountain1.jpg', '/images/mountain2.jpg', '/images/mountain3.jpg'],
    points: ['High Altitude Trails', 'Guided Tours', 'Camping Under Stars', 'Safety Equipment Included'],
    linkUrl: '/trips/mountains',
    linkLabel: 'Learn More',
  },
  {
    id: 2,
    title: 'Discover the Jungles',
    images: ['/images/jungle1.jpg', '/images/jungle2.jpg', '/images/jungle3.jpg', '/images/jungle4.jpg'],
    points: ['Wildlife Safaris', 'Local Guides', 'Bird Watching', 'Eco-Friendly Stays'],
    linkUrl: '/trips/jungles',
    linkLabel: 'Learn More',
  },
];

const EditDiscoverPage: React.FC = () => {
  const router = useRouter();
  const { id } = useParams(); 
  const entryId = Array.isArray(id) ? parseInt(id[0], 10) : parseInt(id || '', 10);

  const [title, setTitle] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [points, setPoints] = useState<string[]>(['', '', '', '']);
  const [linkUrl, setLinkUrl] = useState<string>('');
  const [linkLabel, setLinkLabel] = useState<string>('');

  useEffect(() => {
    const entry = demoEntries.find(e => e.id === entryId);
    if (entry) {
      setTitle(entry.title);
      setImages(entry.images);
      setPoints(entry.points);
      setLinkUrl(entry.linkUrl);
      setLinkLabel(entry.linkLabel);
    }
  }, [entryId]);

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const urls = Array.from(e.target.files).map(file => URL.createObjectURL(file));
    setImages(prev => [...prev, ...urls]);
  };

  const handleRemoveImage = (idx: number) => {
    setImages(prev => prev.filter((_, i) => i !== idx));
  };

  const handlePointChange = (idx: number, value: string) => {
    setPoints(prev => prev.map((pt, i) => i === idx ? value : pt));
  };

  const handleSave = () => {
    console.log('Saving entry', { entryId, title, images, points, linkUrl, linkLabel });
    // TODO: call API to save edits
    router.push('/discover');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Edit Discover Entry #{entryId}</h2>

      {/* Title */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Title</label>
        <input
          type="text"
          value={title}
          onChange={e => setTitle(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Entry title"
        />
      </div>

      {/* Images Upload */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Images</label>
        <input type="file" accept="image/*" multiple onChange={handleImageUpload} />
        <div className="mt-2 grid grid-cols-3 gap-3">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image src={src} alt={`img-${idx}`} width={80} height={80} className="object-cover rounded" />
              <button
                onClick={() => handleRemoveImage(idx)}
                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
              >✕</button>
            </div>
          ))}
        </div>
      </div>

      {/* Points */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Points (4)</label>
        {points.map((pt, idx) => (
          <input
            key={idx}
            type="text"
            value={pt}
            onChange={e => handlePointChange(idx, e.target.value)}
            className="w-full border rounded px-3 py-2 mb-2"
            placeholder={`Point #${idx + 1}`}
          />
        ))}
      </div>

      {/* Link Fields */}
      <div className="mb-6">
        <label className="block font-medium mb-1">Link URL</label>
        <input
          type="text"
          value={linkUrl}
          onChange={e => setLinkUrl(e.target.value)}
          className="w-full border rounded px-3 py-2 mb-2"
          placeholder="Enter URL"
        />
        <label className="block font-medium mb-1">Link Label</label>
        <input
          type="text"
          value={linkLabel}
          onChange={e => setLinkLabel(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter link text"
        />
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={handleSave}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >
          Save Changes
        </button>
        <Link href="/discover">
          <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">
            Cancel
          </button>
        </Link>
      </div>
    </div>
  );
};

export default EditDiscoverPage;
