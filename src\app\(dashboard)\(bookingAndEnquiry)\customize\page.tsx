"use client"

import { useState, useMemo } from "react"
import {
  Table,
  TableHeader,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Trash } from "lucide-react"
import { PaginationControls } from "@/components/common/pagination/pagination-control"
import { CustomizeTrip } from "@/types/booking/customize-trip"

const customizeTripsData: CustomizeTrip[] = [
  {
    id: 1,
    sn: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    country: "USA",
    packageName: "Everest Base Camp Trek",
    type: "Custom Inquiry",
  },
  {
    id: 2,
    sn: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    country: "UK",
    packageName: "Annapurna Circuit",
    type: "Custom Inquiry",
  },
  // …add more rows as needed
]

export default function CustomizeTripsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [entriesPerPage, setEntriesPerPage] = useState(25)
  const [currentPage, setCurrentPage] = useState(1)

  // filter
  const filteredData = useMemo(() => {
    if (!searchTerm) return customizeTripsData
    return customizeTripsData.filter((item) =>
      [String(item.sn), item.name, item.email, item.country, item.packageName, item.type]
        .some((val) =>
          val.toLowerCase().includes(searchTerm.toLowerCase())
        )
    )
  }, [searchTerm])

  // pagination
  const totalPages = Math.ceil(filteredData.length / entriesPerPage)
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * entriesPerPage
    return filteredData.slice(start, start + entriesPerPage)
  }, [filteredData, currentPage, entriesPerPage])

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-4">
        Customize Trips
      </h2>

      <div className="flex flex-wrap items-center justify-between mb-4 gap-4">
        <div className="flex items-center gap-2">
          <span>Show</span>
          <Select
            value={String(entriesPerPage)}
            onValueChange={(val) => {
              setEntriesPerPage(Number(val))
              setCurrentPage(1)
            }}
          >
            <SelectTrigger className="w-[80px]">
              <SelectValue placeholder="Entries" />
            </SelectTrigger>
            <SelectContent>
              {[10, 25, 50, 100].map((n) => (
                <SelectItem key={n} value={String(n)}>
                  {n}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span>entries</span>
        </div>

        <div className="flex items-center gap-2">
          <span>Search:</span>
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-auto"
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S.N.</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Country</TableHead>
              <TableHead>Package Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Options</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row) => (
                <TableRow key={row.id}>
                  <TableCell>{row.sn}</TableCell>
                  <TableCell>{row.name}</TableCell>
                  <TableCell>{row.email}</TableCell>
                  <TableCell>{row.country}</TableCell>
                  <TableCell>{row.packageName}</TableCell>
                  <TableCell>{row.type}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => console.log("View", row.id)}
                      >
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </Button>
                      <Button
                        variant="destructive"
                        size="icon"
                        onClick={() => console.log("Delete", row.id)}
                      >
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No records found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4 flex justify-end">
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          totalEntries={filteredData.length}
          entriesPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  )
}
