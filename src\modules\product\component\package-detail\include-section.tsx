'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface IncludesSectionProps {
    title: string; onTitleChange: (value: string) => void; body: string; onBodyChange: (value: string) => void;
}

export function IncludesSection({ title, onTitleChange, body, onBodyChange }: IncludesSectionProps) {
    return (
        <>
            <Label htmlFor="includes-title">Includes Title</Label>
            <Input id="includes-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <Label>Includes Details</Label>
            <RichTextEditor value={body} onChange={data => onBodyChange(data)} />
        </>
    )
}

// ExcludesSection.tsx is identical, just change the labels/ids.