"use client"
import { CategoryForm, CategoryFormData } from "@/components/category/category-form";
import { useRouter } from "next/navigation";
import React from "react";

export default function CreatePeakClimbingCategory() {
  const router = useRouter();
  const handleSubmit = (data: CategoryFormData) => {
    console.log("Peak Climbing category data:", data);
    router.push("/peak-climbing");

  };

  return (
    <CategoryForm
      title="Add Peak Climbing Category"
      onSubmit={handleSubmit}
      cancelPath="/peak-climbing"
    />
  );
}
