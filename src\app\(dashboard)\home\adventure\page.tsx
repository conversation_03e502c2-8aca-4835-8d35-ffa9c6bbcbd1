"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface DiscoverEntry {
  id: number;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
}

// Demo data; replace with API fetch
const initialEntries: DiscoverEntry[] = [
  {
    id: 1,
    title: 'Explore the Mountains',
    images: ['/images/mountain1.jpg', '/images/mountain2.jpg', '/images/mountain3.jpg'],
    points: ['High Altitude Trails', 'Guided Tours', 'Camping Under Stars', 'Safety Equipment Included'],
    linkUrl: '/trips/mountains',
    linkLabel: 'Learn More',
  },
  {
    id: 2,
    title: 'Discover the Jungles',
    images: ['/images/jungle1.jpg', '/images/jungle2.jpg', '/images/jungle3.jpg', '/images/jungle4.jpg'],
    points: ['Wildlife Safaris', 'Local Guides', 'Bird Watching', 'Eco-Friendly Stays'],
    linkUrl: '/trips/jungles',
    linkLabel: 'Learn More',
  },
];

const DiscoverAdminPage: React.FC = () => {
  const [entries, setEntries] = useState<DiscoverEntry[]>(initialEntries);
  const visibleImages = 2;

  const handleDelete = (id: number) => {
    // TODO: delete via API
    setEntries(entries.filter(e => e.id !== id));
  };

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Discover Entries</h1>
        <Link href="/home/<USER>/create">
          <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
            + Add Entry
          </button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Title</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Images</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Points</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Link</th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {entries.map(entry => (
              <tr key={entry.id}>
                <td className="px-6 py-4 text-sm text-gray-700">{entry.title}</td>
                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    {entry.images.slice(0, visibleImages).map((src, idx) => (
                      <Image
                        key={idx}
                        src={src}
                        alt={`img-${entry.id}-${idx}`}
                        width={64}
                        height={64}
                        className="object-cover rounded"
                      />
                    ))}
                    {entry.images.length > visibleImages && (
                      <div className="h-16 w-16 flex items-center justify-center bg-gray-100 rounded text-gray-600 text-sm">
                        +{entry.images.length - visibleImages}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  <ul className="list-disc list-inside space-y-1">
                    {entry.points.map((pt, idx) => (
                      <li key={idx}>{pt}</li>
                    ))}
                  </ul>
                </td>
                <td className="px-6 py-4 text-sm text-blue-600 hover:underline">
                  <a href={entry.linkUrl}>{entry.linkLabel}</a>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <Link href={`/home/<USER>/edit/${entry.id}`}>
                    <button className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                      Edit
                    </button>
                  </Link>
                  <button
                    onClick={() => handleDelete(entry.id)}
                    className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DiscoverAdminPage;