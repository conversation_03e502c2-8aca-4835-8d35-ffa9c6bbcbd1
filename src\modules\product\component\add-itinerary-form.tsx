'use client'

import { useState, useEffect, FormEvent } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Edit, Save, X } from 'lucide-react'
import { ItineraryItem, AddItineraryFormProps } from "@/types/package-form"

import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

const initialFormState = {
  heading: '',
  day: '',
  title: '',
  trekDistance: '',
  flightHours: '',
  drivingHour: '',
  highestAltitude: '',
  trekDuration: '',
  details: '',
}

export function AddItineraryForm({ 
  editingItem, 
  onAddItinerary, 
  onUpdateItinerary, 
  onCancelEdit 
}: AddItineraryFormProps) {
  const [formData, setFormData] = useState(initialFormState)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const isEditing = editingItem !== null

  // Reset form when editing item changes
  useEffect(() => {
    if (isEditing && editingItem) {
      setFormData({
        heading: editingItem.heading,
        day: editingItem.day,
        title: editingItem.title,
        trekDistance: editingItem.trekDistance,
        flightHours: editingItem.flightHours,
        drivingHour: editingItem.drivingHour,
        highestAltitude: editingItem.highestAltitude,
        trekDuration: editingItem.trekDuration,
        details: editingItem.details,
      })
      setImageFile(null)
    } else {
      setFormData(initialFormState)
      setImageFile(null)
    }
  }, [editingItem, isEditing])

  // Handle input changes
  const handleInputChange = (field: keyof typeof initialFormState, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImageFile(e.target.files[0])
    }
  }

  // Validate form
  const validateForm = (): boolean => {
    if (!formData.day.trim()) {
      alert('Day number is required')
      return false
    }
    if (!formData.title.trim()) {
      alert('Title is required')
      return false
    }
    if (!formData.details.trim()) {
      alert('Activity details are required')
      return false
    }
    return true
  }

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      if (isEditing && editingItem) {
        // Update existing itinerary item
        const updatedItem: ItineraryItem = {
          ...editingItem,
          ...formData,
          imageFile: imageFile || undefined
        }
        onUpdateItinerary(updatedItem)
      } else {
        // Add new itinerary item
        const newItem: Omit<ItineraryItem, 'id'> = {
          ...formData,
          imageFile: imageFile || undefined
        }
        onAddItinerary(newItem)
      }

      // Reset form after successful submission
      setFormData(initialFormState)
      setImageFile(null)
      
      // Clear file input
      const fileInput = document.getElementById('image') as HTMLInputElement
      if (fileInput) fileInput.value = ''
      
    } catch (error) {
      console.error('Error saving itinerary item:', error)
      alert('Error saving itinerary item. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle cancel edit
  const handleCancel = () => {
    setFormData(initialFormState)
    setImageFile(null)
    const fileInput = document.getElementById('image') as HTMLInputElement
    if (fileInput) fileInput.value = ''
    onCancelEdit()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
          {isEditing ? 'Edit Package Itinerary' : 'Add Package Itinerary'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="heading">Heading *</Label>
              <Input 
                id="heading" 
                value={formData.heading} 
                onChange={(e) => handleInputChange('heading', e.target.value)}
                placeholder="Enter heading"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="day">Day Number *</Label>
              <Input 
                id="day" 
                value={formData.day} 
                onChange={(e) => handleInputChange('day', e.target.value)}
                placeholder="e.g., 1, 2, 3"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title *</Label>
            <Input 
              id="title" 
              value={formData.title} 
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="Enter activity title"
              required
            />
          </div>

          <div>
            <Label htmlFor="details">Activity Details *</Label>
            <div className="mt-1">
              <RichTextEditor 
                value={formData.details} 
                onChange={(data) => handleInputChange('details', data)} 
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="trekDistance">Trek Distance</Label>
              <Input 
                id="trekDistance" 
                value={formData.trekDistance} 
                onChange={(e) => handleInputChange('trekDistance', e.target.value)}
                placeholder="e.g., 5km"
              />
            </div>
            
            <div>
              <Label htmlFor="flightHours">Flight Hours</Label>
              <Input 
                id="flightHours" 
                value={formData.flightHours} 
                onChange={(e) => handleInputChange('flightHours', e.target.value)}
                placeholder="e.g., 1.5"
              />
            </div>
            
            <div>
              <Label htmlFor="drivingHour">Driving Hours</Label>
              <Input 
                id="drivingHour" 
                value={formData.drivingHour} 
                onChange={(e) => handleInputChange('drivingHour', e.target.value)}
                placeholder="e.g., 3"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="highestAltitude">Highest Altitude</Label>
              <Input 
                id="highestAltitude" 
                value={formData.highestAltitude} 
                onChange={(e) => handleInputChange('highestAltitude', e.target.value)}
                placeholder="e.g., 4000m"
              />
            </div>
            
            <div>
              <Label htmlFor="trekDuration">Trek Duration</Label>
              <Input 
                id="trekDuration" 
                value={formData.trekDuration} 
                onChange={(e) => handleInputChange('trekDuration', e.target.value)}
                placeholder="e.g., 6 hours"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="image">Image</Label>
            <Input 
              id="image" 
              type="file" 
              accept="image/*"
              onChange={handleImageChange}
              className="mt-1"
            />
            {imageFile && (
              <p className="text-sm text-green-600 mt-1">
                Selected: {imageFile.name}
              </p>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4 border-t">
            {isEditing && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Cancel
              </Button>
            )}
            
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {isEditing ? 'Saving...' : 'Adding...'}
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {isEditing ? 'Save Changes' : 'Add Itinerary'}
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}



// 'use client'

// import { useState, useEffect, FormEvent } from "react"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Plus, Edit } from 'lucide-react'
// import { ItineraryItem } from "../template/package-page"

// import dynamic from 'next/dynamic'

// const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

// interface AddItineraryFormProps {
//   editingItem: ItineraryItem | null;
//   onAddItinerary: (data: Omit<ItineraryItem, 'id'>) => void;
//   onUpdateItinerary: (data: ItineraryItem) => void;
//   onCancelEdit: () => void;
// }

// const initialState = {
//   heading: '',
//   day: '',
//   title: '',
//   trekDistance: '',
//   flightHours: '',
//   drivingHour: '',
//   highestAltitude: '',
//   trekDuration: '',
//   details: '',
// };

// export function AddItineraryForm({ editingItem, onAddItinerary, onUpdateItinerary, onCancelEdit }: AddItineraryFormProps) {
//   const [formData, setFormData] = useState(initialState);
//   const [imageFile, setImageFile] = useState<File | null>(null);

//   const isEditing = editingItem !== null;

//   useEffect(() => {
//     if (isEditing) {
//       setFormData({
//         heading: editingItem.heading,
//         day: editingItem.day,
//         title: editingItem.title,
//         trekDistance: editingItem.trekDistance,
//         flightHours: editingItem.flightHours,
//         drivingHour: editingItem.drivingHour,
//         highestAltitude: editingItem.highestAltitude,
//         trekDuration: editingItem.trekDuration,
//         details: editingItem.details,
//       });
//       setImageFile(null);
//     } else {
//       setFormData(initialState);
//     }
//   }, [editingItem, isEditing]);

//   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     const { id, value } = e.target;
//     setFormData(prev => ({ ...prev, [id]: value }));
//   };

//   const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     if (e.target.files && e.target.files[0]) {
//       setImageFile(e.target.files[0]);
//     }
//   };

//   const handleSubmit = (e: FormEvent) => {
//     e.preventDefault();
//     if (isEditing) {
//       onUpdateItinerary({ ...editingItem, ...formData, imageFile: imageFile || undefined });
//     } else {
//       onAddItinerary({ ...formData, imageFile: imageFile || undefined });
//     }
//     setFormData(initialState);
//     setImageFile(null);
//     const fileInput = document.getElementById('image') as HTMLInputElement;
//     if (fileInput) fileInput.value = '';
//   };

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle className="flex items-center gap-2">
//           {isEditing ? <Edit className="w-5 h-5" /> : <Plus className="w-5 h-5" />}
//           {isEditing ? 'Edit Package Itinerary' : 'Add Package Itinerary'}
//         </CardTitle>
//       </CardHeader>
//       <CardContent>
//         <form onSubmit={handleSubmit} className="space-y-4">
//           <div>
//             <Label htmlFor="heading">Heading</Label><Input id="heading" value={formData.heading} onChange={handleChange} placeholder="Enter Heading" />
//           </div>
//           <div>
//             <Label htmlFor="day">Day Number</Label><Input id="day" value={formData.day} onChange={handleChange} placeholder="Enter Day Number" />
//           </div>
//           <div>
//             <Label htmlFor="title">Title</Label><Input id="title" value={formData.title} onChange={handleChange} placeholder="Enter Title" />
//           </div>
//           <div>
//             <Label htmlFor="details">Activity Details</Label>
//             <RichTextEditor value={formData.details} onChange={data => setFormData(prev => ({ ...prev, details: data }))} />
//           </div>
//           <div>
//             <Label htmlFor="trekDistance">Trek Distance</Label><Input id="trekDistance" value={formData.trekDistance} onChange={handleChange} placeholder="e.g., 5km" />
//           </div>
//           <div>
//             <Label htmlFor="flightHours">Flight Hours</Label><Input id="flightHours" value={formData.flightHours} onChange={handleChange} placeholder="e.g., 1.5" />
//           </div>
//           <div>
//             <Label htmlFor="drivingHour">Driving Hour</Label><Input id="drivingHour" value={formData.drivingHour} onChange={handleChange} placeholder="e.g., 3" />
//           </div>
//           <div>
//             <Label htmlFor="highestAltitude">Highest Altitude</Label><Input id="highestAltitude" value={formData.highestAltitude} onChange={handleChange} placeholder="e.g., 4000m" />
//           </div>
//           <div>
//             <Label htmlFor="trekDuration">Trek Duration</Label><Input id="trekDuration" value={formData.trekDuration} onChange={handleChange} placeholder="e.g., 6 hours" />
//           </div>
//           <div>
//             <Label htmlFor="image">Image</Label><Input id="image" type="file" onChange={handleImageChange} />
//           </div>

//           <div className="flex justify-end gap-2 pt-2">
//             {isEditing && (
//               // The `w-full` class has been removed
//               <Button type="button" variant="outline" onClick={onCancelEdit}>
//                 Cancel
//               </Button>
//             )}
//             {/* The `w-full` class has been removed */}
//             <Button type="submit">
//               {isEditing ? 'Save Changes' : 'Add Itinerary'}
//             </Button>
//           </div>
//         </form>
//       </CardContent>
//     </Card>
//   )
// }