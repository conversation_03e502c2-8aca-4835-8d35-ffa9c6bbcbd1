import  { LoginCredentials, LoginResponse } from "@/types/user";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";


export const useLoginMutation = (onLoginSuccess: (response: LoginResponse) => void) => { // ✅ Pass LoginResponse, not User
  const queryClient = useQueryClient();
  
  return useMutation<LoginResponse, Error, LoginCredentials>({ // ✅ Return LoginResponse, not User
    mutationFn: ({ email, password }: LoginCredentials) =>
      fetch(`https://api.trailandtreknepal.com/auth/login`, {
        mode: "cors",
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: (response: LoginResponse) => { // ✅ Get full response
      queryClient.invalidateQueries({ queryKey: ["user"] });
      toast.success("Logged in successfully");
      
      // ✅ Extract user data from response.data
      const userData = response.data;
      onLoginSuccess(); // Pass just the user data
    },
    onError: (error) => {
      toast.error("Error logging in: " + error.message);
    },
  });
};