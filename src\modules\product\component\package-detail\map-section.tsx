'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { FileUpload } from "../fileupload"

interface MapSectionProps {
    title: string
    onTitleChange: (value: string) => void
    onFileChange: (file: File | null) => void
}

export function MapSection({ title, onTitleChange, onFileChange }: MapSectionProps) {
    return (
        <>
            <Label htmlFor="map-title">Map Title</Label>
            <Input id="map-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <FileUpload label="Map Image" accept="image/*" showPreview={true} onFileChange={onFileChange} />
        </>
    )
}