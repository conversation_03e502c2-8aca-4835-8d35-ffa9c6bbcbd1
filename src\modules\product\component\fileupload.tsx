'use client'
import { useState, ChangeEvent } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Image from 'next/image';

interface FileUploadProps {
    label: string;
    accept: string;
    onFileChange: (file: File | null) => void;
    showPreview?: boolean;
    previewSrc?: string;
    previewAlt?: string;
}

export function FileUpload({ label, accept, onFileChange, showPreview = false, previewSrc, previewAlt = "Preview" }: FileUploadProps) {
    const [currentPreview, setCurrentPreview] = useState<string | null>(previewSrc || null);

    const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files ? event.target.files[0] : null;
        onFileChange(file);
        if (file && showPreview) {
            setCurrentPreview(URL.createObjectURL(file));
        } else {
            setCurrentPreview(previewSrc || null);
        }
    };

    return (
        <div>
            <Label>{label}</Label>
            <Input type="file" accept={accept} onChange={handleFileChange} className="mt-1" />
            {showPreview && currentPreview && (
                <div className="mt-4">
                    <Image src={currentPreview} alt={previewAlt} width={120} height={80} className="object-cover rounded-md border" />
                </div>
            )}
        </div>
    );
}

// 'use client'

// import React, { useRef, useState, ChangeEvent } from 'react'
// import { Button } from "@/components/ui/button"
// import { Upload } from 'lucide-react'
// import Image from 'next/image'

// interface FileUploadProps {
//   label?: string
//   accept?: string
//   showPreview?: boolean
//   previewSrc?: string   // optional external preview override
//   previewAlt?: string
//   onFileChange?: (file: File | null) => void
// }

// export function FileUpload({
//   label = "Upload Image",
//   accept = "image/*",
//   showPreview = true,
//   previewSrc,
//   previewAlt,
//   onFileChange,
// }: FileUploadProps) {
//   const fileInputRef = useRef<HTMLInputElement>(null)
//   const [fileName, setFileName] = useState<string>("No file chosen")
//   const [localPreview, setLocalPreview] = useState<string | null>(null)

//   const handleButtonClick = () => {
//     fileInputRef.current?.click()
//   }

//   const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
//     const file = e.target.files?.[0] ?? null
//     if (file) {
//       setFileName(file.name)
//       const url = URL.createObjectURL(file)
//       setLocalPreview(url)
//       onFileChange?.(file)
//     } else {
//       setFileName("No file chosen")
//       setLocalPreview(null)
//       onFileChange?.(null)
//     }
//   }

//   // Decide what to show for preview:
//   const src = previewSrc || localPreview

//   return (
//     <div>
//       {label && <p className="text-sm font-medium mb-1">{label}</p>}

//       <div className="flex items-center gap-2">
//         <Button variant="outline" size="sm" onClick={handleButtonClick}>
//           <Upload className="w-4 h-4 mr-1" />
//           Choose File
//         </Button>
//         <span className="text-sm text-gray-500">{fileName}</span>
//       </div>

//       <input
//         ref={fileInputRef}
//         type="file"
//         accept={accept}
//         className="hidden"
//         onChange={handleFileChange}
//       />

//       {showPreview && src && (
//         <div className="mt-3">
//           <Image
//             src={src}
//             alt={previewAlt || "Preview"}
//             width={150}
//             height={100}
//             className="w-32 h-20 object-cover rounded border"
//           />
//         </div>
//       )}
//     </div>
//   )
// }
