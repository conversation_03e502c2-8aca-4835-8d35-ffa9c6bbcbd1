'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface HighlightSectionProps {
    title: string
    onTitleChange: (value: string) => void
    body: string
    onBodyChange: (value: string) => void
}

export function HighlightSection({ title, onTitleChange, body, onBodyChange }: HighlightSectionProps) {
    return (
        <>
            <Label htmlFor="highlights-title">Trip Highlights Title</Label>
            <Input id="highlights-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <Label>Trip Highlights Description</Label>
            <RichTextEditor value={body} onChange={data => onBodyChange(data)} />
        </>
    )
}