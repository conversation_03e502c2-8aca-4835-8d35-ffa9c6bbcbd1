"use client";

import React, {
  useState,
  useCallback,
} from "react";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface Testimonial {
  id: number;
  youtubeUrl: string;
  title: string;
  destination: string;
  date: string;
}

const DEMO_HEADING = "They Talk About Us";
const DEMO_SUBHEADING = "Hear From Our Satisfied Travelers";

const initialTestimonials: Testimonial[] = [
  {
    id: 1,
    youtubeUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    title: "EXPERIENCE NEPAL",
    destination: "Nepal",
    date: "2025-07",
  },
  {
    id: 2,
    youtubeUrl: "https://www.youtube.com/watch?v=xyz123",
    title: "DAVE AND DAPHNE CANADA",
    destination: "Everest 3 Passes Trek",
    date: "2024-01",
  },
  {
    id: 3,
    youtubeUrl: "https://www.youtube.com/watch?v=abc456",
    title: "STUDENT FROM USA",
    destination: "Mardi Himal Base Camp",
    date: "2024-04",
  },
];

export default function TestimonialsPage() {
  const [heading, setHeading] = useState<string>(DEMO_HEADING);
  const [subheading, setSubheading] = useState<string>(DEMO_SUBHEADING);
  const [tempHeading, setTempHeading] = useState<string>(heading);
  const [tempSubheading, setTempSubheading] = useState<string>(
    subheading
  );
  const [isSectionOpen, setIsSectionOpen] = useState(false);

  const [testimonials, setTestimonials] = useState<
    Testimonial[]
  >(initialTestimonials);

  const [isEditOpen, setIsEditOpen] = useState(false);
  const [current, setCurrent] = useState<Testimonial | null>(null);
  const [tempUrl, setTempUrl] = useState("");
  const [tempTitle, setTempTitle] = useState("");
  const [tempDest, setTempDest] = useState("");
  const [tempDate, setTempDate] = useState("");

  const openSection = useCallback(() => {
    setTempHeading(heading);
    setTempSubheading(subheading);
    setIsSectionOpen(true);
  }, [heading, subheading]);
  const saveSection = useCallback(() => {
    setHeading(tempHeading);
    setSubheading(tempSubheading);
    setIsSectionOpen(false);
  }, [tempHeading, tempSubheading]);

  const openEdit = useCallback((t: Testimonial | null) => {
    setCurrent(t);
    setTempUrl(t?.youtubeUrl ?? "");
    setTempTitle(t?.title ?? "");
    setTempDest(t?.destination ?? "");
    setTempDate(t?.date ?? "");
    setIsEditOpen(true);
  }, []);
  const saveEdit = useCallback(() => {
    if (current) {
      setTestimonials((prev) =>
        prev.map((x) =>
          x.id === current.id
            ? {
                ...x,
                youtubeUrl: tempUrl,
                title: tempTitle,
                destination: tempDest,
                date: tempDate,
              }
            : x
        )
      );
    } else {
      const nextId =
        testimonials.length > 0
          ? Math.max(...testimonials.map((x) => x.id)) + 1
          : 1;
      setTestimonials((prev) => [
        ...prev,
        {
          id: nextId,
          youtubeUrl: tempUrl,
          title: tempTitle,
          destination: tempDest,
          date: tempDate,
        },
      ]);
    }
    setIsEditOpen(false);
  }, [
    current,
    testimonials,
    tempUrl,
    tempTitle,
    tempDest,
    tempDate,
  ]);
  const handleDelete = useCallback((id: number) => {
    setTestimonials((prev) => prev.filter((x) => x.id !== id));
  }, []);

  return (
    <div className="p-6 bg-gray-50 min-h-screen space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{heading}</h1>
          <p className="text-gray-600 mt-1">{subheading}</p>
        </div>

        <Dialog
          open={isSectionOpen}
          onOpenChange={setIsSectionOpen}
        >
          <DialogTrigger asChild>
            <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={openSection}>
              {heading && subheading
                ? "Edit Heading"
                : "+ Add Heading"}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {heading && subheading
                  ? "Edit Section"
                  : "Add Section Heading"}
              </DialogTitle>
              <DialogDescription>
                {heading && subheading
                  ? "Change main heading and subheading."
                  : "Enter a heading and subheading."}
              </DialogDescription>
            </DialogHeader>
            <div className="mt-2 space-y-4">
              <div>
                <label className="block font-medium mb-1">
                  Heading
                </label>
                <input
                  type="text"
                  value={tempHeading}
                  onChange={(e) =>
                    setTempHeading(e.target.value)
                  }
                  className="w-full border rounded px-3 py-2"
                />
              </div>
              <div>
                <label className="block font-medium mb-1">
                  Subheading
                </label>
                <textarea
                  value={tempSubheading}
                  onChange={(e) =>
                    setTempSubheading(e.target.value)
                  }
                  className="w-full border rounded px-3 py-2"
                  rows={2}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsSectionOpen(false)}
              >
                Cancel
              </Button>
              <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveSection}>Save</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">
          Testimonials
        </h2>
        <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={() => openEdit(null)}>
          + Add Testimonial
        </Button>
      </div>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {current
                ? `Edit Testimonial #${current.id}`
                : "Add Testimonial"}
            </DialogTitle>
            <DialogDescription>
              Enter video link, title, destination, and date.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-2 space-y-4">
            <div>
              <label className="block font-medium mb-1">
                YouTube URL
              </label>
              <input
                type="text"
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Title
              </label>
              <input
                type="text"
                value={tempTitle}
                onChange={(e) =>
                  setTempTitle(e.target.value)
                }
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Destination
              </label>
              <input
                type="text"
                value={tempDest}
                onChange={(e) =>
                  setTempDest(e.target.value)
                }
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Date
              </label>
              <input
                type="month"
                value={tempDate}
                onChange={(e) =>
                  setTempDate(e.target.value)
                }
                className="w-full border rounded px-3 py-2"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditOpen(false)}
            >
              Cancel
            </Button>
            <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveEdit}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Video
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Title
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Destination
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Date
              </th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {testimonials.map((t) => (
              <tr key={t.id}>
                <td className="px-6 py-4 text-blue-600 hover:underline">
                  <a href={t.youtubeUrl} target="_blank">
                    View
                  </a>
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {t.title}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {t.destination}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {new Date(t.date + "-01").toLocaleString(
                    "default",
                    { year: "numeric", month: "long" }
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className='bg-brand text-white hover:bg-brand/80 hover:text-white'
                    onClick={() => openEdit(t)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(t.id)}
                  >
                    Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
