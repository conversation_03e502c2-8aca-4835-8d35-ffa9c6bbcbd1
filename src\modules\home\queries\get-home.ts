import { IHome } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useQuery } from "@tanstack/react-query";

export function useGetHome() {
    return useQuery<IApiResponse<IHome>, Error>({
        queryKey: ["home"],
        queryFn: () =>
            fetch(`/api/home`, {
                mode: "cors",
                credentials: "include",
            })
                .then((res) => res.json()),
    })
}