'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import dynamic from 'next/dynamic';
import { SeoDetailsSection } from '@/components/package/seo-detail-section';
import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section';
import { FileUpload } from '@/modules/product/component/fileupload';
import { CollapsibleSection } from '@/modules/product/component/collapsible';
import { BlogFormData } from '@/types/blogs';

const CkEditor = dynamic(() => import("@/utils/ck-editor"), { ssr: false });

const EditBlogPage = () => {
  const router = useRouter();
  const params = useParams();
  const blogId = params?.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [openSeo, setOpenSeo] = useState(true);
  const [openSchema, setOpenSchema] = useState(false);
  const [openContent, setOpenContent] = useState(true);

  const [formData, setFormData] = useState<BlogFormData>({
    title: '',
    slug: '',
    description: '',
    content: '',
    imageAltTag: '',
    image: null,
    published: false,
    metaTitle: '',
    metaDescription: '',
    metaKeywords: '',
    canonicalUrl: '',
    schema: ''
  });

  const [existingImageUrl, setExistingImageUrl] = useState<string>('');

  const generateSlug = (title: string) =>
    title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)+/g, '');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    if (name === 'title') {
      setFormData(prev => ({
        ...prev,
        slug: generateSlug(value)
      }));
    }
  };

  const handleContentChange = (data: string) => {
    setFormData(fd => ({ ...fd, content: data }));
  };

  useEffect(() => {
    setTimeout(() => {
      const dummyBlog = {
        title: 'Dummy Blog Post',
        slug: 'dummy-blog-post',
        description: 'This is a short description of the dummy blog post.',
        content: '<p>This is the dummy content with <strong>rich text</strong>.</p>',
        imageAltTag: 'Dummy alt text for banner',
        image: '/images/random.jpeg',
        published: true,
        metaTitle: 'Dummy Blog Meta Title',
        metaDescription: 'Dummy blog meta description for SEO.',
        metaKeywords: 'dummy, blog, seo',
        canonicalUrl: 'https://example.com/dummy-blog-post',
        schema: '{"@context": "https://schema.org"}'
      };

      setFormData({
        ...dummyBlog,
        image: null  
      });
      setExistingImageUrl(dummyBlog.image);
      setIsFetching(false);
    }, 500);
  }, [blogId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    console.log('Updating blog data:', formData);

    setTimeout(() => {
      alert('Dummy update complete!');
      setIsLoading(false);
      router.push('/blogs');
    }, 800);
  };

  if (isFetching) {
    return <div className="p-8">Loading blog data...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="container mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Link href="/blogs" className="text-brand hover:text-brand/80">← Back to Blogs</Link>
          <h1 className="text-2xl font-bold text-brand">Edit Blog</h1>
        </div>

        <div className="space-y-6">
          <CollapsibleSection title="SEO Details" isOpen={openSeo} onToggle={() => setOpenSeo(v => !v)}>
            <SeoDetailsSection formData={formData} setFormData={(seoFields) => setFormData(fd => ({ ...fd, ...seoFields }))} />
          </CollapsibleSection>

          <CollapsibleSection title="Schema Details" isOpen={openSchema} onToggle={() => setOpenSchema(v => !v)}>
            <SchemaDetailsSection schema={formData.schema ?? ''} setSchema={(schema) => setFormData(fd => ({ ...fd, schema }))} />
          </CollapsibleSection>

          <CollapsibleSection title="Blog Content" isOpen={openContent} onToggle={() => setOpenContent(v => !v)}>
            <div className="bg-white p-4 shadow-lg">
              <form onSubmit={handleSubmit} className="space-y-6">

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      required
                      value={formData.title}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                    <input
                      type="text"
                      id="slug"
                      name="slug"
                      required
                      value={formData.slug}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">Short Description *</label>
                  <textarea
                    id="description"
                    name="description"
                    required
                    rows={3}
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                  />
                </div>

                <div>
                  <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">Content *</label>
                  <CkEditor value={formData.content} onChange={handleContentChange} />
                </div>

                <div>
                  <label htmlFor="imageAltTag" className="block text-sm font-medium text-gray-700 mb-2">Image Alt Tag</label>
                  <input
                    type="text"
                    id="imageAltTag"
                    name="imageAltTag"
                    value={formData.imageAltTag}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <FileUpload
                  label="Banner Image"
                  accept="image/*"
                  onFileChange={(file) => setFormData(prev => ({ ...prev, image: file }))}
                  showPreview={true}
                  previewSrc={
                    formData.image ? URL.createObjectURL(formData.image) : existingImageUrl
                  }
                  previewAlt="Banner Image Preview"
                />

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="published"
                    name="published"
                    checked={formData.published}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="published" className="text-sm font-medium text-gray-700">Publish immediately</label>
                </div>

                <div className="flex justify-end space-x-4">
                  <Link href="/blogs">
                    <Button type="button" variant="outline" className="px-6">Cancel</Button>
                  </Link>
                  <Button type="submit" disabled={isLoading} className="bg-brand hover:bg-brand/80 px-6">
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                        </svg>
                        Updating...
                      </>
                    ) : 'Update Blog'}
                  </Button>
                </div>

              </form>
            </div>
          </CollapsibleSection>

        </div>
      </div>
    </div>
  );
};

export default EditBlogPage;
