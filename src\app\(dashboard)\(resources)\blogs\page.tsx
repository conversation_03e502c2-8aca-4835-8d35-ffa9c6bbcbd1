'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { BlogFormData } from '@/types/blogs';

const mockBlogs: BlogFormData[] = [
  {
    id: 1,
    title: 'Introduction: Why Climb the Himalaya Ama Dablam?',
    slug: 'ama-dablam-expedition-guide',
    description: 'Ama <PERSON>blam rises above Nepal\'s Khumbu Valley at 6,812 m (22,334 ft), its elegant pyramid shape draws the attention of seasoned climbers to Nepal every year.',
    content: '<p>This is the dummy content with <strong>rich text</strong>.</p>',
    imageAltTag: 'Ama Dablam',
    image: null,
    published: true,
    metaTitle: 'Ama Dablam Expedition Guide',
    metaDescription: 'Ama Dablam rises above Nepal\'s Khumbu Valley at 6,812 m (22,334 ft), its elegant pyramid shape draws the attention of seasoned climbers to Nepal every year.',
    metaKeywords: 'ama dablam, nepal, climbing, expedition',
    canonicalUrl: 'https://example.com/ama-dablam-expedition-guide',
    schema: '{"@context": "https://schema.org"}'
  },
  {
    id: 2,
    title: 'North Nepal Travel & Trek: Best Trekking Agency in Pokhara for the Annapurna Region Trek',
    slug: 'annapurna-trekking-agency',
    description: 'If you\'re dreaming of exploring the stunning trails of the Annapurna region, Pokhara is your starting point.',
    content: '<p>This is the dummy content with <strong>rich text</strong>.</p>',
    imageAltTag: 'Annapurna Trek',
    image: null,
    published: true,
    metaTitle: 'Annapurna Trekking Agency',
    metaDescription: 'If you\'re dreaming of exploring the stunning trails of the Annapurna region, Pokhara is your starting point.',
    metaKeywords: 'annapurna, trekking, agency, pokhara',
    canonicalUrl: 'https://example.com/annapurna-trekking-agency',
    schema: '{"@context": "https://schema.org"}'
  },
  {
    id: 3,
    title: 'Best Trekking Agency in Pokhara: North Nepal Trek',
    slug: 'best-trekking-agency-pokhara',
    description: 'Looking to travel to Nepal? Choose the best travel agency to make your vacation dreams come true!',
    content: '<p>This is the dummy content with <strong>rich text</strong>.</p>',
    imageAltTag: 'Best Trekking Agency',
    image: null,
    published: false,
    metaTitle: 'Best Trekking Agency in Pokhara',
    metaDescription: 'Looking to travel to Nepal? Choose the best travel agency to make your vacation dreams come true!',
    metaKeywords: 'trekking, agency, pokhara',
    canonicalUrl: 'https://example.com/best-trekking-agency-pokhara',
    schema: '{"@context": "https://schema.org"}'
  }
];

const BlogsPage = () => {
  const [blogs, setBlogs] = useState<BlogFormData[]>([]);
  const [entriesPerPage, setEntriesPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    setBlogs(mockBlogs);
  }, []);

  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this blog?')) {
      setBlogs(blogs.filter(blog => blog.id !== id));
    }
  };

  // Filter blogs based on search term
  const filteredBlogs = blogs.filter(blog =>
    blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    blog.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalEntries = filteredBlogs.length;
  const startEntry = totalEntries > 0 ? (currentPage - 1) * entriesPerPage + 1 : 0;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);
  const paginatedBlogs = filteredBlogs.slice((currentPage - 1) * entriesPerPage, currentPage * entriesPerPage);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="container mx-auto">

        <div className="flex justify-between items-center mb-8">
          <h1 className="text-2xl font-bold text-brand">Blogs</h1>
          <Link
            href="/blogs/create"
          >
            <Button className="bg-brand hover:bg-brand/80">
              Add Blogs
            </Button>
          </Link>
        </div>
        {/* Header Controls */}
        <div className="flex justify-between items-center mb-6 bg-white p-4 rounded-lg shadow-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-700">Show</span>
            <select
              value={entriesPerPage}
              onChange={(e) => {
                setEntriesPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border border-gray-300 rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            <span className="text-gray-700">entries</span>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-gray-700">Search:</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setCurrentPage(1);
              }}
              className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Search..."
            />
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                  SN
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                  NAME
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                  SLUG
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                  PUBLISHED
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-500 uppercase tracking-wider">
                  ACTIONS
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {paginatedBlogs.map((blog, index) => (
                <tr key={blog.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {startEntry + index}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {blog.title}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {blog.slug}
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded ${blog.published
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                      }`}>
                      {blog.published ? 'Published' : 'Draft'}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex space-x-2">
                      <Link
                        href={`/blogs/edit/${blog.id}`}
                        className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={() => handleDelete(blog.id!)}
                        className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                      >
                        delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredBlogs.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No blogs found.</p>
            </div>
          )}
        </div>

        {/* Footer with pagination info */}
        {totalEntries > 0 && (
          <div className="flex justify-between items-center mt-4 px-4">
            <div className="text-sm text-gray-700">
              Showing {startEntry} to {endEntry} of {totalEntries} entries
            </div>

            {Math.ceil(totalEntries / entriesPerPage) > 1 && (
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
                >
                  Previous
                </button>
                <span className="px-3 py-1 bg-blue-500 text-white text-sm rounded">
                  {currentPage}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, Math.ceil(totalEntries / entriesPerPage)))}
                  disabled={currentPage === Math.ceil(totalEntries / entriesPerPage)}
                  className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700 disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BlogsPage;