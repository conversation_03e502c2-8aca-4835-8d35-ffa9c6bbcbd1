"use client";

import React, {
  useState,
  use<PERSON><PERSON>back,
  ChangeEvent,
} from "react";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

interface Review {
  id: number;
  imageFile?: File;
  imagePreview?: string;
  text: string;
  name: string;
  destination: string;
  date: string;
}

const DEMO_HEADING = "What our peers say about us";
const DEMO_PARAGRAPH1 = `Every trek we organize is more than just a trip—it's a deeply personal journey 
through some of the most stunning landscapes on Earth.`;
const DEMO_PARAGRAPH2 = `Our guides aren't just trained professionals—they're passionate storytellers, 
mountain lovers, and lifelong companions who make sure every step you take is meaningful.`;

const initialReviews: Review[] = [
  {
    id: 1,
    imagePreview: "/images/sofia.jpg",
    text: "The itinerary design balanced challenge & rest perfectly.",
    name: "<PERSON>",
    destination: "<PERSON><PERSON>al",
    date: "2023-12",
  },
  {
    id: 2,
    imagePreview: "/images/liam.jpg",
    text: "A flawless journey with top-notch support.",
    name: "<PERSON>",
    destination: "Annapurna Circuit",
    date: "2024-02",
  },
];

export default function ReviewsAdminPage() {
  const [heading, setHeading] = useState(DEMO_HEADING);
  const [para1, setPara1] = useState(DEMO_PARAGRAPH1);
  const [para2, setPara2] = useState(DEMO_PARAGRAPH2);
  const [tempHeading, setTempHeading] = useState(heading);
  const [tempPara1, setTempPara1] = useState(para1);
  const [tempPara2, setTempPara2] = useState(para2);
  const [isSectionOpen, setIsSectionOpen] = useState(false);

  const [reviews, setReviews] = useState<Review[]>(initialReviews);

  const [isEditOpen, setIsEditOpen] = useState(false);
  const [current, setCurrent] = useState<Review | null>(null);
  const [tempFile, setTempFile] = useState<File>();
  const [tempPreview, setTempPreview] = useState<string>("");
  const [tempText, setTempText] = useState("");
  const [tempName, setTempName] = useState("");
  const [tempDestination, setTempDestination] = useState("");
  const [tempDate, setTempDate] = useState("");

  const openSection = useCallback(() => {
    setTempHeading(heading);
    setTempPara1(para1);
    setTempPara2(para2);
    setIsSectionOpen(true);
  }, [heading, para1, para2]);

  const saveSection = useCallback(() => {
    setHeading(tempHeading);
    setPara1(tempPara1);
    setPara2(tempPara2);
    setIsSectionOpen(false);
  }, [tempHeading, tempPara1, tempPara2]);

  const openEdit = useCallback((r: Review | null) => {
    setCurrent(r);
    setTempFile(r?.imageFile);
    setTempPreview(r?.imagePreview || "");
    setTempText(r?.text || "");
    setTempName(r?.name || "");
    setTempDestination(r?.destination || "");
    setTempDate(r?.date || "");
    setIsEditOpen(true);
  }, []);

  const onFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setTempFile(f);
    setTempPreview(URL.createObjectURL(f));
  };

  const saveEdit = useCallback(() => {
    if (current) {
      setReviews((prev) =>
        prev.map((x) =>
          x.id === current.id
            ? {
                ...x,
                imageFile: tempFile,
                imagePreview: tempPreview,
                text: tempText,
                name: tempName,
                destination: tempDestination,
                date: tempDate,
              }
            : x
        )
      );
    } else {
      const nextId =
        reviews.length > 0
          ? Math.max(...reviews.map((x) => x.id)) + 1
          : 1;
      setReviews((prev) => [
        ...prev,
        {
          id: nextId,
          imageFile: tempFile,
          imagePreview: tempPreview,
          text: tempText,
          name: tempName,
          destination: tempDestination,
          date: tempDate,
        },
      ]);
    }
    setIsEditOpen(false);
  }, [
    current,
    reviews,
    tempFile,
    tempPreview,
    tempText,
    tempName,
    tempDestination,
    tempDate,
  ]);

  const handleDelete = useCallback((id: number) => {
    setReviews((prev) => prev.filter((x) => x.id !== id));
  }, []);

  return (
    <div className="p-6 bg-gray-50 min-h-screen space-y-6">
      <div className="flex justify-between items-start space-x-4">
        <div className="flex-1 space-y-2">
          <h1 className="text-3xl font-bold">{heading}</h1>
          <p className="text-gray-700 line-clamp-3">{para1}</p>
          <p className="text-gray-700 line-clamp-3">{para2}</p>
        </div>
        <Dialog
          open={isSectionOpen}
          onOpenChange={setIsSectionOpen}
        >
          <DialogTrigger asChild>
            <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={openSection}>
              {heading && para1 && para2
                ? "Edit Section"
                : "+ Add Section Text"}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {heading && para1 && para2
                  ? "Edit Section"
                  : "Add Section Text"}
              </DialogTitle>
              <DialogDescription>
                Update heading and two paragraphs.
              </DialogDescription>
            </DialogHeader>
            <div className="mt-2 space-y-4">
              <div>
                <label className="block font-medium mb-1">
                  Heading
                </label>
                <input
                  type="text"
                  value={tempHeading}
                  onChange={(e) =>
                    setTempHeading(e.target.value)
                  }
                  className="w-full border rounded px-3 py-2"
                />
              </div>
              <div>
                <label className="block font-medium mb-1">
                  Paragraph 1
                </label>
                <textarea
                  rows={3}
                  value={tempPara1}
                  onChange={(e) =>
                    setTempPara1(e.target.value)
                  }
                  className="w-full border rounded px-3 py-2"
                />
              </div>
              <div>
                <label className="block font-medium mb-1">
                  Paragraph 2
                </label>
                <textarea
                  rows={3}
                  value={tempPara2}
                  onChange={(e) =>
                    setTempPara2(e.target.value)
                  }
                  className="w-full border rounded px-3 py-2"
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsSectionOpen(false)}
              >
                Cancel
              </Button>
              <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveSection}>Save</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">Reviews</h2>
        <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={() => openEdit(null)}>
          + Add Review
        </Button>
      </div>

      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {current ? `Edit Review #${current.id}` : "Add Review"}
            </DialogTitle>
            <DialogDescription>
              Upload image + fill in text, name, destination & date.
            </DialogDescription>
          </DialogHeader>
          <div className="mt-2 space-y-4">
            <div>
              <label className="block font-medium mb-1">
                Photo
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={onFileChange}
              />
              {tempPreview && (
                <Image
                  src={tempPreview}
                  alt="preview"
                  width={100}
                  height={100}
                  className="mt-2 h-24 w-24 object-cover rounded"
                />
              )}
            </div>
            <div>
              <label className="block font-medium mb-1">
                Quote Text
              </label>
              <textarea
                rows={3}
                value={tempText}
                onChange={(e) => setTempText(e.target.value)}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Name
              </label>
              <input
                type="text"
                value={tempName}
                onChange={(e) => setTempName(e.target.value)}
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Destination
              </label>
              <input
                type="text"
                value={tempDestination}
                onChange={(e) =>
                  setTempDestination(e.target.value)
                }
                className="w-full border rounded px-3 py-2"
              />
            </div>
            <div>
              <label className="block font-medium mb-1">
                Date
              </label>
              <input
                type="month"
                value={tempDate}
                onChange={(e) => setTempDate(e.target.value)}
                className="w-full border rounded px-3 py-2"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditOpen(false)}
            >
              Cancel
            </Button>
            <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={saveEdit}>Save</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Photo
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Quote
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Name
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Destination
              </th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">
                Date
              </th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {reviews.map((r) => (
              <tr key={r.id}>
                <td className="px-6 py-4">
                  {r.imagePreview && (
                    <Image
                      src={r.imagePreview}
                      alt={r.name}
                      width={100}
                      height={100}
                      className="h-16 w-16 object-cover rounded"
                    />
                  )}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {r.text}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {r.name}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {r.destination}
                </td>
                <td className="px-6 py-4 text-sm text-gray-700">
                  {new Date(r.date + "-01").toLocaleString(
                    "default",
                    {
                      month: "long",
                      year: "numeric",
                    }
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className='bg-brand text-white hover:bg-brand/80 hover:text-white'
                    onClick={() => openEdit(r)}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(r.id)}
                  >
                    Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
