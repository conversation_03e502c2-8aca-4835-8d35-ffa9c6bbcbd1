"use client"
import { CategoryForm, CategoryFormData } from "@/components/category/category-form";
import { useRouter } from "next/navigation";
import React from "react";

export default function CreateFastpackingCategory() {
  const router = useRouter();
  const handleSubmit = (data: CategoryFormData) => {
    console.log("Fastpacking category data:", data);
    router.push("/fastpacking");

  };

  return (
    <CategoryForm
      title="Add Fastpacking Category"
      onSubmit={handleSubmit}
      cancelPath="/fastpacking"
    />
  );
}
