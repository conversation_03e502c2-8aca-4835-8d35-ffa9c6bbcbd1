Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA5DCA0000 ntdll.dll
7FFA5C270000 KERNEL32.DLL
7FFA5AE80000 KERNELBASE.dll
7FFA5BDB0000 USER32.dll
000210040000 msys-2.0.dll
7FFA5B8C0000 win32u.dll
7FFA5BF80000 GDI32.dll
7FFA5B8F0000 gdi32full.dll
7FFA5ADD0000 msvcp_win.dll
7FFA5B4C0000 ucrtbase.dll
7FFA5BFB0000 advapi32.dll
7FFA5C070000 msvcrt.dll
7FFA5DB90000 sechost.dll
7FFA5C150000 RPCRT4.dll
7FFA5A3C0000 CRYPTBASE.DLL
7FFA5B6A0000 bcryptPrimitives.dll
7FFA5BC40000 IMM32.DLL
