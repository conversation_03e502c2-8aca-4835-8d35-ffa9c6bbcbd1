"use client"

import { useState, useMemo } from "react"
import {
  Table,
  TableHeader,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
} from "@/components/ui/table"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select"
import { But<PERSON> } from "@/components/ui/button"
import { Eye, Trash } from "lucide-react"
import { PaginationControls } from "@/components/common/pagination/pagination-control"
import { BookingInfo } from "@/types/booking/booking"

const bookingInfoData: BookingInfo[] = [
  {
    id: 1,
    sn: 1,
    name: "MD <PERSON>",
    email: "<EMAIL>",
    country: "Bangladesh",
    packageName: "Adventure PackageInquire",
    type: "PackageInquire",
    bookDate: "2025-Jun-03",
    departureDate: "2025-Jun-03",
  },
  {
    id: 2,
    sn: 2,
    name: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    country: "France",
    packageName: "Khopra Ridge Trek",
    type: "PackageInquire",
    bookDate: "2025-May-29",
    departureDate: "1970-Jan-01",
  },
  {
    id: 3,
    sn: 3,
    name: "Zoe Rawson",
    email: "<EMAIL>",
    country: "United Kingdom",
    packageName: "Mardi Himal Trek",
    type: "PackageInquire",
    bookDate: "2025-May-09",
    departureDate: "1970-Jan-01",
  },
  {
    id: 4,
    sn: 4,
    name: "SATYA DEV",
    email: "<EMAIL>",
    country: "India",
    packageName: "Adventure PackageInquire",
    type: "PackageInquire",
    bookDate: "2025-Jun-05",
    departureDate: "2025-May-07",
  },
  {
    id: 5,
    sn: 5,
    name: "MEHULKUMAR CHAUHAN",
    email: "<EMAIL>",
    country: "India",
    packageName: "Kailash Manasarovar tour from Nepal 2025 open now!",
    type: "PackageInquire",
    bookDate: "2025-May-06",
    departureDate: "1970-Jan-01",
  },
  {
    id: 6,
    sn: 6,
    name: "Philip Wrangberg",
    email: "<EMAIL>",
    country: "Sweden",
    packageName: "Manaslu Circuit Trekking Nepal",
    type: "PackageInquire",
    bookDate: "2025-Jun-01",
    departureDate: "1970-Jan-01",
  },
]

export default function TrekkingHikingBookingsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [entriesPerPage, setEntriesPerPage] = useState(25)
  const [currentPage, setCurrentPage] = useState(1)

  // 1. Filter by search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return bookingInfoData
    return bookingInfoData.filter((item) =>
      Object.values(item).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      )
    )
  }, [searchTerm])

  // 2. Pagination calculations
  const totalPages = Math.ceil(filteredData.length / entriesPerPage)
  const paginatedData = useMemo(() => {
    const start = (currentPage - 1) * entriesPerPage
    return filteredData.slice(start, start + entriesPerPage)
  }, [filteredData, currentPage, entriesPerPage])

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="text-2xl font-semibold mb-4">
        Trekking And Hiking Bookings
      </h2>

      {/* Controls */}
      <div className="flex flex-wrap items-center justify-between mb-4 gap-4">
        <div className="flex items-center gap-2">
          <span>Show</span>
          <Select
            value={String(entriesPerPage)}
            onValueChange={(val) => {
              setEntriesPerPage(Number(val))
              setCurrentPage(1)
            }}
          >
            <SelectTrigger className="w-[80px]">
              <SelectValue placeholder="Entries" />
            </SelectTrigger>
            <SelectContent>
              {[10, 25, 50, 100].map((n) => (
                <SelectItem key={n} value={String(n)}>
                  {n}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span>entries</span>
        </div>

        <div className="flex items-center gap-2">
          <span>Search:</span>
          <Input
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value)
              setCurrentPage(1)
            }}
            className="w-auto"
          />
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S.N.</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Country</TableHead>
              <TableHead>Package Name</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Book Date</TableHead>
              <TableHead>Departure Date</TableHead>
              <TableHead>Options</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.length > 0 ? (
              paginatedData.map((row) => (
                <TableRow key={row.id}>
                  <TableCell>{row.sn}</TableCell>
                  <TableCell>{row.name}</TableCell>
                  <TableCell>{row.email}</TableCell>
                  <TableCell>{row.country}</TableCell>
                  <TableCell>{row.packageName}</TableCell>
                  <TableCell>{row.type}</TableCell>
                  <TableCell>{row.bookDate}</TableCell>
                  <TableCell>{row.departureDate}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon">
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View</span>
                      </Button>
                      <Button variant="destructive" size="icon">
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="mt-4 flex justify-end">
        <PaginationControls
          currentPage={currentPage}
          totalPages={totalPages}
          totalEntries={filteredData.length}
          entriesPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
        />
      </div>
    </div>
  )
}
