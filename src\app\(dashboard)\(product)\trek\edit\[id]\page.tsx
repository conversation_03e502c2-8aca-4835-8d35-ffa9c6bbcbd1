"use client";

import React, { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { CategoryForm, CategoryFormData } from "@/components/category/category-form";

const demoCategories: (CategoryFormData & { id: number })[] = [
  {
    id: 1,
    name: "Annapurna Base Camp",
    slug: "annapurna-base-camp",
    altText: "ABC view",
    imagePreview: "/images/abc-thumb.jpg",
    description: "Iconic trek through diverse landscapes.",
    published: true,
  },
  {
    id: 2,
    name: "Everest Base Camp",
    slug: "everest-base-camp",
    altText: "EBC view",
    imagePreview: "/images/ebc-thumb.jpg",
    description: "Sunrise vistas of Everest.",
    published: true,
  },
];

export default function EditTrekCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const rawId = Array.isArray(params.id) ? params.id[0] : params.id;
  const categoryId = rawId ? parseInt(rawId, 10) : null;

  const [initialData, setInitialData] =
    useState<CategoryFormData | null>(null);

  useEffect(() => {
    if (categoryId !== null) {
      const found = demoCategories.find((c) => c.id === categoryId);
      if (found) {
        const {  ...form } = found;
        setInitialData(form);
      }
    }
  }, [categoryId]);

  if (initialData === null) {
    return <p className="p-6">Loading…</p>;
  }

  const handleSubmit = (data: CategoryFormData) => {
    console.log("Updated category:", categoryId, data);
    router.push("/trek");
  };

  return (
    <CategoryForm
      title="Edit Trek Category"
      initialData={initialData}
      onSubmit={handleSubmit}
      cancelPath="/trek"
    />
  );
}
