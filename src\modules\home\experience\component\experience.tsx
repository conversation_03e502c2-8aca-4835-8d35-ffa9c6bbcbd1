"use client";

import React, { useState, useC<PERSON>back, ChangeEvent } from 'react';
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface FeatureEntry {
  id: number;
  title: string;
  subtitle: string;
}

interface SectionHeadingDialogProps {
  heading: string;
  subheading: string;
  onSave: (newHeading: string, newSubheading: string) => void;
}

interface FeatureDialogProps {
  entry: FeatureEntry | null;
  onSave: (entry: FeatureEntry | null, title: string, subtitle: string) => void;
}

const initialEntries: FeatureEntry[] = [
  { id: 1, title: 'EXPLORE PLACES YOU COULDN\'T YOURSELF', subtitle: 'All trips are led by certified expert guides, unlocking life experiences in places most never see.' },
  { id: 2, title: 'JOIN A SMALL LIKE-MINDED GROUP', subtitle: '75% join our trips as solo travellers, with most in their 30s–50s. 95% give our group dynamic 5 stars.' },
  { id: 3, title: 'HASSLE-FREE FROM START TO FINISH', subtitle: 'We’ve sorted the logistics, so you can just rock up and have a blast in the wild.' },
];

const SectionHeadingDialog = React.memo<SectionHeadingDialogProps>(function SectionHeadingDialog({ heading, subheading, onSave }) {
  const [open, setOpen] = useState(false);
  const [tempHeading, setTempHeading] = useState(heading);
  const [tempSubheading, setTempSubheading] = useState(subheading);

  const handleOpen = useCallback(() => {
    setTempHeading(heading);
    setTempSubheading(subheading);
    setOpen(true);
  }, [heading, subheading]);

  const handleSave = useCallback(() => {
    onSave(tempHeading, tempSubheading);
    setOpen(false);
  }, [onSave, tempHeading, tempSubheading]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button onClick={handleOpen} className='bg-brand text-white hover:bg-brand/80'>Edit Heading</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Section Heading</DialogTitle>
          <DialogDescription>Update the main heading and subheading.</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block font-medium mb-1">Heading</label>
            <input
              type="text"
              value={tempHeading}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setTempHeading(e.target.value)}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div>
            <label className="block font-medium mb-1">Subheading</label>
            <textarea
              value={tempSubheading}
              onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setTempSubheading(e.target.value)}
              className="w-full border rounded px-3 py-2"
              rows={2}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
          <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={handleSave}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});
SectionHeadingDialog.displayName = "SectionHeadingDialog";

const FeatureDialog = React.memo<FeatureDialogProps>(function FeatureDialog({ entry, onSave }) {
  const [open, setOpen] = useState(false);
  const [tempTitle, setTempTitle] = useState(entry?.title || '');
  const [tempSubtitle, setTempSubtitle] = useState(entry?.subtitle || '');

  const handleOpen = useCallback(() => {
    setTempTitle(entry?.title || '');
    setTempSubtitle(entry?.subtitle || '');
    setOpen(true);
  }, [entry]);

  const handleSave = useCallback(() => {
    onSave(entry, tempTitle, tempSubtitle);
    setOpen(false);
  }, [entry, onSave, tempTitle, tempSubtitle]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" onClick={handleOpen} className='bg-brand text-white hover:bg-brand/80 hover:text-white'>
          {entry ? 'Edit' : '+ Add Feature'}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{entry ? `Edit Feature #${entry.id}` : 'Add Feature'}</DialogTitle>
          <DialogDescription>{entry ? 'Modify feature details.' : 'Enter details for new feature.'}</DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="block font-medium mb-1">Title</label>
            <input
              type="text"
              value={tempTitle}
              onChange={(e: ChangeEvent<HTMLInputElement>) => setTempTitle(e.target.value)}
              className="w-full border rounded px-3 py-2"
            />
          </div>
          <div>
            <label className="block font-medium mb-1">Subtitle</label>
            <textarea
              value={tempSubtitle}
              onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setTempSubtitle(e.target.value)}
              className="w-full border rounded px-3 py-2"
              rows={2}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
          <Button className='bg-brand text-white hover:bg-brand/80 hover:text-white' onClick={handleSave}>Save</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});
FeatureDialog.displayName = "FeatureDialog";

const ExperiencePage: React.FC = () => {
  const [sectionHeading, setSectionHeading] = useState<string>('Experience Nature Like Never Before');
  const [sectionSubheading, setSectionSubheading] = useState<string>(
    'Join us for unforgettable adventures in breathtaking landscapes. Our guided tours ensure safety and enjoyment for all levels of experience.'
  );

  const [entries, setEntries] = useState<FeatureEntry[]>(initialEntries);

  const handleSaveSection = useCallback((newHeading: string, newSubheading: string) => {
    setSectionHeading(newHeading);
    setSectionSubheading(newSubheading);
  }, []);

  const handleSaveEntry = useCallback((entry: FeatureEntry | null, title: string, subtitle: string) => {
    if (entry) {
      setEntries(prev => prev.map(e => e.id === entry.id ? { ...e, title, subtitle } : e));
    } else {
      setEntries(prev => {
        const nextId = prev.length ? Math.max(...prev.map(e => e.id)) + 1 : 1;
        return [...prev, { id: nextId, title, subtitle }];
      });
    }
  }, []);

  const handleDelete = useCallback((id: number) => {
    setEntries(prev => prev.filter(e => e.id !== id));
  }, []);

  return (
    <div className="p-6 bg-gray-100 min-h-screen space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-1">{sectionHeading}</h1>
          <p className="text-gray-600">{sectionSubheading}</p>
        </div>
        <SectionHeadingDialog
          heading={sectionHeading}
          subheading={sectionSubheading}
          onSave={handleSaveSection}
        />
      </div>

      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold">Experience Features</h2>
        {entries.length < 3 && <FeatureDialog entry={null} onSave={handleSaveEntry} />}
      </div>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Title</th>
              <th className="px-6 py-3 text-sm font-medium text-gray-500 uppercase">Subtitle</th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {entries.map(entry => (
              <tr key={entry.id}>
                <td className="px-6 py-4 text-sm text-gray-700">{entry.title}</td>
                <td className="px-6 py-4 text-sm text-gray-700">{entry.subtitle}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <FeatureDialog entry={entry} onSave={handleSaveEntry} />
                  <Button variant="destructive" size="sm" onClick={() => handleDelete(entry.id)}>
                    Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ExperiencePage;
