"use client";

import React, { useState, ChangeEvent } from "react";
import Link from "next/link";
import Image from "next/image";
import dynamic from "next/dynamic";
import { Button } from "@/components/ui/button";

const CkEditor = dynamic(() => import("@/utils/ck-editor"), {
  ssr: false,
});

export interface CategoryFormData {
  name: string;
  slug: string;
  altText: string;
  imageFile?: File;
  imagePreview?: string;
  description: string;
  published: boolean;
}

export interface CategoryFormProps {
  title: string;
  initialData?: Partial<CategoryFormData>;
  onSubmit: (data: CategoryFormData) => void;
  cancelPath?: string;
}

export function CategoryForm({
  title,
  initialData = {},
  onSubmit,
  cancelPath,
}: CategoryFormProps) {
  const [formData, setFormData] = useState<CategoryFormData>({
    name: "",
    slug: "",
    altText: "",
    imageFile: undefined,
    imagePreview: undefined,
    description: "",
    published: false,
    ...initialData,
  });

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((fd) => ({
      ...fd,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFile = (e: ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    setFormData((fd) => ({
      ...fd,
      imageFile: f,
      imagePreview: URL.createObjectURL(f),
    }));
  };

  const handleDescriptionChange = (data: string) => {
    setFormData((fd) => ({ ...fd, description: data }));
  };

  const handleSubmit = () => {
    onSubmit(formData);
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen space-y-6">
      <h1 className="text-2xl font-semibold">{title}</h1>
      <div className="space-y-4 container mx-auto">
        <div>
          <label className="block mb-1 font-medium">Name</label>
          <input
            name="name"
            type="text"
            value={formData.name}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          />
        </div>

        <div>
          <label className="block mb-1 font-medium">Slug</label>
          <input
            name="slug"
            type="text"
            value={formData.slug}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          />
        </div>

        <div>
          <label className="block mb-1 font-medium">
            Image Alt Tag
          </label>
          <input
            name="altText"
            type="text"
            value={formData.altText}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          />
        </div>

        <div>
          <label className="block mb-1 font-medium">Image</label>
          <input
            type="file"
            accept="image/*"
            onChange={handleFile}
          />
          {formData.imagePreview && (
            <div className="mt-2">
              <Image
                src={formData.imagePreview}
                alt={formData.altText || formData.name}
                width={240}
                height={160}
                className="object-cover rounded"
              />
            </div>
          )}
        </div>

        <div>
          <label className="block mb-1 font-medium">
            Description
          </label>
          <CkEditor
            value={formData.description}
            onChange={handleDescriptionChange}
          />
        </div>

        {/* Publish toggle */}
        <div className="flex items-center space-x-2">
          <input
            id="publish"
            name="published"
            type="checkbox"
            checked={formData.published}
            onChange={handleChange}
          />
          <label
            htmlFor="publish"
            className="font-medium"
          >
            Publish
          </label>
        </div>

        <div className="flex space-x-2">
          <Button onClick={handleSubmit}>
            {title.includes("Add") ? "Create" : "Save"}
          </Button>
          {cancelPath ? (
            <Link href={cancelPath}>
              <Button variant="outline">Cancel</Button>
            </Link>
          ) : (
            <Button
              variant="outline"
              onClick={() => history.back()}
            >
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}


// "use client"
// import { useState } from "react"
// import type React from "react"
// import { useRouter } from "next/navigation"
// import { Button } from "@/components/ui/button"
// import { Input } from "@/components/ui/input"
// import { Label } from "@/components/ui/label"
// import { Checkbox } from "@/components/ui/checkbox"
// import dynamic from "next/dynamic"

// const CkEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false });

// interface CategoryFormProps {
//   title: string
//   onSubmit: (data: CategoryFormData) => void
//   initialData?: Partial<CategoryFormData>
//   backUrl?: string
// }

// export interface CategoryFormData {
//   name: string
//   slug: string
//   imageAltTag: string
//   image: File | null
//   description: string
//   published: boolean
// }

// export function CategoryForm({ title, onSubmit, initialData, backUrl }: CategoryFormProps) {
//   const router = useRouter()
//   const [formData, setFormData] = useState<CategoryFormData>({
//     name: initialData?.name || "",
//     slug: initialData?.slug || "",
//     imageAltTag: initialData?.imageAltTag || "",
//     image: initialData?.image || null,
//     description: initialData?.description || "",
//     published: initialData?.published || false,
//   })

//   const handleNameChange = (name: string) => {
//     const slug = name
//       .toLowerCase()
//       .replace(/[^a-z0-9]+/g, "-")
//       .replace(/(^-|-$)/g, "")
//     setFormData((prev) => ({ ...prev, name, slug }))
//   }

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault()
//     onSubmit(formData)
//   }

//   return (
//     <div className="container mx-auto p-6">
//       <div className="flex items-center justify-between mb-6">
//         <h1 className="text-2xl font-bold">{title}</h1>
//         {backUrl && (
//           <Button variant="outline" onClick={() => router.push(backUrl)}>
//             Back to List
//           </Button>
//         )}
//       </div>

//       <form onSubmit={handleSubmit} className="space-y-6">
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//           <div>
//             <Label htmlFor="name">Name</Label>
//             <Input
//               id="name"
//               value={formData.name}
//               onChange={(e) => handleNameChange(e.target.value)}
//               placeholder="Enter Category Name"
//               required
//             />
//           </div>

//           <div>
//             <Label htmlFor="slug">Slug</Label>
//             <Input
//               id="slug"
//               value={formData.slug}
//               onChange={(e) => setFormData((prev) => ({ ...prev, slug: e.target.value }))}
//               placeholder="Enter Category Slug"
//               required
//             />
//           </div>
//         </div>

//         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//           <div>
//             <Label htmlFor="imageAltTag">Image Alt Tag</Label>
//             <Input
//               id="imageAltTag"
//               value={formData.imageAltTag}
//               onChange={(e) => setFormData((prev) => ({ ...prev, imageAltTag: e.target.value }))}
//               placeholder="Enter Alt Tag Details"
//             />
//           </div>

//           <div>
//             <Label htmlFor="image">Image</Label>
//             <Input
//               id="image"
//               type="file"
//               accept="image/*"
//               onChange={(e) => setFormData((prev) => ({ ...prev, image: e.target.files?.[0] || null }))}
//             />
//           </div>
//         </div>

//         <div>
//           <Label htmlFor="description">Description</Label>
//           {/* <RichTextEditor
//             value={formData.description}
//             onChange={(description) => setFormData((prev) => ({ ...prev, description }))}
//             placeholder="Enter description..."
//           /> */}

//           <CkEditor
//             value={formData.description}
//             onChange={(e) => {
//               setFormData({
//                 ...formData,
//                 description: e,
//               });
//             }}
//           />
//         </div>

//         <div className="flex items-center space-x-2">
//           <Checkbox
//             id="published"
//             checked={formData.published}
//             onCheckedChange={(checked) => setFormData((prev) => ({ ...prev, published: !!checked }))}
//           />
//           <Label htmlFor="published">Publish</Label>
//         </div>

//         <Button type="submit" className="bg-gray-600 hover:bg-gray-700">
//           Submit
//         </Button>
//       </form>
//     </div>
//   )
// }
