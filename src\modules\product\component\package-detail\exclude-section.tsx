// IncludesSection.tsx
'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface ExcludesSectionProps {
    title: string; onTitleChange: (value: string) => void; body: string; onBodyChange: (value: string) => void;
}

export function ExcludesSection({ title, onTitleChange, body, onBodyChange }: ExcludesSectionProps) {
    return (
        <>
            <Label htmlFor="exludes-title">Exludes Title</Label>
            <Input id="exludes-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <Label>Excludes Details</Label>
            <RichTextEditor value={body} onChange={data => onBodyChange(data)} />
        </>
    )
}

// ExcludesSection.tsx is identical, just change the labels/ids.