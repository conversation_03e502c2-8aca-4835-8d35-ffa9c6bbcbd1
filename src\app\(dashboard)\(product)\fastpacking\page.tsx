"use client"

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { CategoriesList, Category } from "@/components/category/category-list";

export default function FastpackingListPage() {
  const router = useRouter();
  const [treks, setTreks] = useState<Category[]>([
    { id: 1, name: "Annapurna Base Camp", slug: "abc", published: true },
    { id: 2, name: "Everest Base Camp", slug: "ebc", published: true },
  ]);

  return (
    <CategoriesList
      title="All Fastpacking"
      categories={treks}
      createUrl="/fastpacking/create"
      onEdit={(id) => router.push(`/fastpacking/edit/${id}`)}
      onDelete={(id) => setTreks((t) => t.filter((x) => x.id !== id))}
    />
  );
}
