"use client"
import { CategoryForm, CategoryFormData } from "@/components/category/category-form";
import { useRouter } from "next/navigation";
import React from "react";

export default function CreateTrekCategory() {
  const router = useRouter();
  const handleSubmit = (data: CategoryFormData) => {
    console.log("Tour category data:", data);
    router.push("/trek");

  };

  return (
    <CategoryForm
      title="Add Tour Category"
      onSubmit={handleSubmit}
      cancelPath="/trek"
    />
  );
}
