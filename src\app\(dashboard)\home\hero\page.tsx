"use client";

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { IHero } from '@/types/home';
import { UseGetHero } from '@/modules/home/<USER>/queries/use-get-home';
import { UseDeleteHero } from '@/modules/home/<USER>/mutations/use-delete-hero';

const HeroListPage: React.FC = () => {
  const { data: heroResponse, isLoading, error, refetch } = UseGetHero();
  const deleteHeroMutation = UseDeleteHero();
  const hero: IHero | null = heroResponse?.data || null;
  
  const visibleImages = 2;

  const handleDelete = async () => {
    if (!hero) return;
    
    if (window.confirm('Are you sure you want to delete this hero section? This action cannot be undone.')) {
      try {
        await deleteHeroMutation.mutateAsync(hero);
      } catch (error) {
        console.error('Failed to delete hero:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-gray-600">Loading hero data...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-gray-100 min-h-screen">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-red-600 mb-4">Error loading hero data</div>
            <Button onClick={() => refetch()} className="bg-brand text-white">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-semibold">Hero Section</h1>
        {hero ? (
          <Link href={`/home/<USER>/edit/${hero.id}`}>
            <button 
              className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80 disabled:bg-gray-400 disabled:cursor-not-allowed"
              disabled={deleteHeroMutation.isPending}
            >
              Edit Hero
            </button>
          </Link>
        ) : (
          <Link href="/home/<USER>/create">
            <button className="px-4 py-2 bg-brand text-white rounded hover:bg-brand/80">
              + Add Hero
            </button>
          </Link>
        )}
      </div>

      <div className="bg-white rounded-lg shadow overflow-x-auto">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Video
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Images
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Titles
              </th>
              <th className="px-6 py-3 text-left text-sm font-medium text-gray-500 uppercase">
                Subtitles
              </th>
              <th className="px-6 py-3"></th>
            </tr>
          </thead>
          <tbody>
            {hero ? (
              <tr className="border-t">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {hero.videoUrl ? (
                    <Link href={hero.videoUrl} target="_blank">
                      <span className="text-brand hover:underline">View</span>
                    </Link>
                  ) : (
                    <span className="text-gray-400">No video</span>
                  )}
                </td>

                <td className="px-6 py-4">
                  <div className="flex items-center space-x-2">
                    {hero.images && hero.images.length > 0 ? (
                      <>
                        {hero.images.slice(0, visibleImages).map((src, idx) => (
                          <Image
                            key={idx}
                            src={src}
                            alt={`hero-img-${idx}`}
                            width={64}
                            height={64}
                            className="object-cover rounded"
                          />
                        ))}
                        {hero.images.length > visibleImages && (
                          <div className="h-16 w-16 flex items-center justify-center bg-gray-100 rounded text-gray-600 text-sm">
                            +{hero.images.length - visibleImages}
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="text-gray-400 text-sm">No images</div>
                    )}
                  </div>
                </td>

                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                  {hero.titles && hero.titles.length > 0 ? (
                    <div className="space-y-1">
                      {hero.titles.map((title, idx) => (
                        <div key={idx} className="font-medium">
                          {title}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <span className="text-gray-400">No titles</span>
                  )}
                </td>

                <td className="px-6 py-4 whitespace-normal text-sm text-gray-700">
                  {hero.subtitles && hero.subtitles.length > 0 ? (
                    <div className="space-y-1">
                      {hero.subtitles.map((subtitle, idx) => (
                        <div key={idx} className="text-gray-600">
                          {subtitle}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <span className="text-gray-400">No subtitles</span>
                  )}
                </td>

                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                  <Link href={`/home/<USER>/edit/${hero.id}`}>
                    <Button 
                      className="px-3 py-1 bg-brand text-white rounded hover:bg-brand/80"
                      disabled={deleteHeroMutation.isPending}
                    >
                      Edit
                    </Button>
                  </Link>
                  <Button
                    onClick={handleDelete}
                    disabled={deleteHeroMutation.isPending}
                    className={`px-3 py-1 text-white rounded ${
                      deleteHeroMutation.isPending
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-red-600 hover:bg-red-700'
                    }`}
                  >
                    {deleteHeroMutation.isPending ? 'Deleting...' : 'Delete'}
                  </Button>
                </td>
              </tr>
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="text-lg">No hero section found</div>
                    <Link href="/home/<USER>/create">
                      <Button className="bg-brand text-white">
                        Create Hero Section
                      </Button>
                    </Link>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HeroListPage;