"use client";

import React, { useState, ChangeEvent } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

const CreateDiscoverPage: React.FC = () => {
  const [title, setTitle] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [points, setPoints] = useState<string[]>(['', '', '', '']);
  const [linkUrl, setLinkUrl] = useState<string>('');
  const [linkLabel, setLinkLabel] = useState<string>('');
  const router = useRouter();

  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    const urls = Array.from(e.target.files).map((file) => URL.createObjectURL(file));
    setImages([...images, ...urls]);
  };
  const handleRemoveImage = (idx: number) => setImages(images.filter((_, i) => i !== idx));

  const handlePointChange = (idx: number, value: string) => {
    const copy = [...points];
    copy[idx] = value;
    setPoints(copy);
  };

  const handleCreate = () => {
    console.log('Creating discover entry', { title, images, points, linkUrl, linkLabel });
    // TODO: call API
    router.push('/home/<USER>');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h2 className="text-2xl font-semibold mb-4">Add Discover Entry</h2>

      {/* Title */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter entry title"
        />
      </div>

      {/* Images */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Images</label>
        <input type="file" accept="image/*" multiple onChange={handleImageUpload} />
        <div className="mt-2 grid grid-cols-3 gap-3">
          {images.map((src, idx) => (
            <div key={idx} className="relative">
              <Image src={src} alt={`img-${idx}`} width={80} height={80} className="object-cover rounded" />
              <button
                onClick={() => handleRemoveImage(idx)}
                className="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full p-1"
              >✕</button>
            </div>
          ))}
        </div>
      </div>

      {/* Points */}
      <div className="mb-4">
        <label className="block font-medium mb-1">Points (4)</label>
        {points.map((pt, idx) => (
          <input
            key={idx}
            type="text"
            value={pt}
            onChange={(e) => handlePointChange(idx, e.target.value)}
            className="w-full border rounded px-3 py-2 mb-2"
            placeholder={`Point #${idx + 1}`}
          />
        ))}
      </div>

      {/* Link */}
      <div className="mb-6">
        <label className="block font-medium mb-1">Link URL</label>
        <input
          type="text"
          value={linkUrl}
          onChange={(e) => setLinkUrl(e.target.value)}
          className="w-full border rounded px-3 py-2 mb-2"
          placeholder="Enter URL"
        />
        <label className="block font-medium mb-1">Link Label</label>
        <input
          type="text"
          value={linkLabel}
          onChange={(e) => setLinkLabel(e.target.value)}
          className="w-full border rounded px-3 py-2"
          placeholder="Enter link text"
        />
      </div>

      {/* Actions */}
      <div className="flex space-x-2">
        <button
          onClick={handleCreate}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
        >Create</button>
        <Link href="/discover">
          <button className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500">Cancel</button>
        </Link>
      </div>
    </div>
  );
};

export default CreateDiscoverPage;
