'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Edit, Trash2, Image as ImageIcon, MapPin, Clock, Mountain } from 'lucide-react'
import Image from "next/image"
import { ItineraryListProps } from "@/types/package-form"

export function ItineraryList({ items, onEdit, onDelete }: ItineraryListProps) {
  
  // Handle delete with confirmation
  const handleDelete = (id: number, title: string) => {
    if (window.confirm(`Are you sure you want to delete "${title}"?`)) {
      onDelete(id)
    }
  }

  // Truncate text helper
  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  // Strip HTML tags for display
  const stripHtml = (html: string) => {
    const tmp = document.createElement('div')
    tmp.innerHTML = html
    return tmp.textContent || tmp.innerText || ''
  }

  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Package Itinerary List
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
              <MapPin className="w-8 h-8 text-gray-400" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">No itinerary items yet</h3>
              <p className="text-gray-500 mt-1">Start building your package itinerary by adding the first day.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Package Itinerary List
          </div>
          <Badge variant="secondary" className="text-sm">
            {items.length} {items.length === 1 ? 'Day' : 'Days'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">Day</TableHead>
                <TableHead className="min-w-[200px]">Title & Details</TableHead>
                <TableHead className="w-24">Image</TableHead>
                <TableHead className="min-w-[120px]">Trek Info</TableHead>
                <TableHead className="w-32 text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                      {item.day}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1">
                      <h4 className="font-medium text-gray-900 leading-tight" title={item.title}>
                        {truncateText(item.title, 60)}
                      </h4>
                      <p className="text-sm text-gray-600 leading-tight" title={stripHtml(item.details)}>
                        {truncateText(stripHtml(item.details), 80)}
                      </p>
                      {item.heading && (
                        <Badge variant="outline" className="text-xs">
                          {truncateText(item.heading, 20)}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="flex items-center justify-center">
                      {item.image ? (
                        <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                          <Image 
                            src={item.image} 
                            alt={item.title} 
                            fill
                            className="object-cover"
                            sizes="64px"
                          />
                        </div>
                      ) : (
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                          <ImageIcon className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell>
                    <div className="space-y-1 text-xs text-gray-600">
                      {item.trekDistance && (
                        <div className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          <span>{item.trekDistance}</span>
                        </div>
                      )}
                      {item.trekDuration && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{item.trekDuration}</span>
                        </div>
                      )}
                      {item.highestAltitude && (
                        <div className="flex items-center gap-1">
                          <Mountain className="w-3 h-3" />
                          <span>{item.highestAltitude}</span>
                        </div>
                      )}
                      {(item.flightHours !== '0' && item.flightHours) && (
                        <div className="text-blue-600">
                          ✈️ {item.flightHours}h
                        </div>
                      )}
                      {(item.drivingHour !== '0' && item.drivingHour) && (
                        <div className="text-green-600">
                          🚗 {item.drivingHour}h
                        </div>
                      )}
                    </div>
                  </TableCell>
                  
                  <TableCell className="text-right">
                    <div className="flex gap-1 justify-end">
                      <Button 
                        onClick={() => onEdit(item.id)} 
                        size="sm" 
                        variant="outline" 
                        className="text-blue-600 border-blue-200 hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                        title="Edit itinerary item"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        onClick={() => handleDelete(item.id, item.title)} 
                        size="sm" 
                        variant="outline" 
                        className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700 hover:border-red-300"
                        title="Delete itinerary item"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        
        {/* Summary Footer */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Total itinerary items: {items.length}</span>
            <span>
              Days covered: {items.length > 0 ? `Day 1 to Day ${Math.max(...items.map(item => parseInt(item.day) || 0))}` : 'None'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}





// 'use client'

// import { Button } from "@/components/ui/button"
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
// import { Edit, Trash2 } from 'lucide-react'
// import Image from "next/image"
// import { ItineraryItem } from "../template/package-page"

// interface ItineraryListProps {
//   items: ItineraryItem[];
//   onEdit: (id: number) => void;
//   onDelete: (id: number) => void;
// }

// export function ItineraryList({ items, onEdit, onDelete }: ItineraryListProps) {
//   if (items.length === 0) {
//     return (
//       <Card>
//         <CardHeader><CardTitle>Package Itinerary List</CardTitle></CardHeader>
//         <CardContent><p>No itinerary items have been added yet.</p></CardContent>
//       </Card>
//     );
//   }

//   return (
//     <Card>
//       <CardHeader><CardTitle>Package Itinerary List</CardTitle></CardHeader>
//       <CardContent>
//         <Table>
//           <TableHeader>
//             <TableRow>
//               <TableHead>Day</TableHead>
//               <TableHead>Title</TableHead>
//               <TableHead>Details</TableHead>
//               <TableHead>Image</TableHead>
//               <TableHead className="text-right">Options</TableHead>
//             </TableRow>
//           </TableHeader>
//           <TableBody>
//             {items.map((item) => (
//               <TableRow key={item.id}>
//                 <TableCell className="font-medium">{item.day}.</TableCell>
//                 <TableCell title={item.title}>
//                   <p className="truncate w-32 md:w-40">
//                     {item.title}
//                   </p>
//                 </TableCell>
//                 <TableCell title={item.details}>
//                   <p className="text-sm text-gray-600 truncate w-32 md:w-40">
//                     {item.details}
//                   </p>
//                 </TableCell>
//                 <TableCell>
//                   {item.image && (
//                     <Image src={item.image} alt={item.title} width={64} height={64} className="w-16 h-16 object-cover rounded-md" />
//                   )}
//                 </TableCell>
//                 <TableCell className="text-right">
//                   <div className="flex gap-2 justify-end">
//                     <Button onClick={() => onEdit(item.id)} size="sm" variant="outline" className="text-green-600 border-green-600 hover:bg-green-50 hover:text-green-700">
//                       <Edit className="w-4 h-4" />
//                     </Button>
//                     <Button onClick={() => onDelete(item.id)} size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50 hover:text-red-700">
//                       <Trash2 className="w-4 h-4" />
//                     </Button>
//                   </div>
//                 </TableCell>
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </CardContent>
//     </Card>
//   )
// }