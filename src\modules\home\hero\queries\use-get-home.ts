import { IHero } from "@/types/home";
import { IApiResponse } from "@/types/response";
import { useQuery } from "@tanstack/react-query";

export function UseGetHero() {
    return useQuery<IApiResponse<IHero>, Error>({
        queryKey: ["hero"],
        queryFn: () =>
            fetch(`https://api.trailandtreknepal.com/home-hero`, {
                mode: "cors",
                credentials: "include",
                headers: {
                    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************.2GDaO61E4S3Z69L3ia7HrJeAJ3A7_yeieBy08Ff06t8",
                }
            })
                .then((res) => res.json()),
    })
}