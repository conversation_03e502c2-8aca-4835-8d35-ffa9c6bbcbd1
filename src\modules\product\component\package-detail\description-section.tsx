'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface DescriptionSectionProps {
    title: string
    onTitleChange: (value: string) => void
    body: string
    onBodyChange: (value: string) => void
}

export function DescriptionSection({ title, onTitleChange, body, onBodyChange }: DescriptionSectionProps) {
    return (
        <>
            <Label htmlFor="description-title">Description Title</Label>
            <Input id="description-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <Label>Description</Label>
            <RichTextEditor value={body} onChange={data => onBodyChange(data)} />
        </>
    )
}