export interface ProductType {
  key: string
  label: string
  categoryUrl: string
  packageUrl: string
}

export const PRODUCT_TYPES: ProductType[] = [
  {
    key: "tour",
    label: "Tour",
    categoryUrl: "/tour",
    packageUrl: "/tourpackage",
  },
  {
    key: "adventure",
    label: "Adventure",
    categoryUrl: "/adventure",
    packageUrl: "/adventurepackage",
  },
  {
    key: "trekking",
    label: "Trekking",
    categoryUrl: "/trekking",
    packageUrl: "/trekkingpackage",
  },
]

export const getProductTypeConfig = (type: string): ProductType | null => {
  return PRODUCT_TYPES.find((pt) => pt.key === type) || null
}

export const isValidProductType = (type: string): boolean => {
  return PRODUCT_TYPES.some((pt) => pt.key === type)
}
