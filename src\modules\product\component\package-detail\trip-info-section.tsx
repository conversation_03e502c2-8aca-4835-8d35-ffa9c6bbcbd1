'use client'
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'
import dynamic from 'next/dynamic'

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

export interface InfoEntry { title: string; body: string; note: string; }

interface TripInfoSectionProps {
    title: string
    onTitleChange: (value: string) => void
    infos: InfoEntry[]
    onAddInfo: () => void
    onRemoveInfo: (index: number) => void
    onUpdateInfo: (index: number, field: "title" | "body" | "note", value: string) => void
}

export function TripInfoSection({ title, onTitleChange, infos, onAddInfo, onRemoveInfo, onUpdateInfo }: TripInfoSectionProps) {
    return (
        <>
            <Label htmlFor="tripInfo-title">TripInfo Title</Label>
            <Input id="tripInfo-title" value={title} onChange={e => onTitleChange(e.target.value)} className="mt-1 mb-4" />
            <div className="flex justify-end mb-4">
                <Button variant="outline" size="sm" onClick={onAddInfo}>+ Add Info Section</Button>
            </div>
            {infos.length === 0 && <p className="text-gray-500 text-center py-8">No info sections yet.</p>}
            {infos.map((info, idx) => (
                <div key={idx} className="relative space-y-4 mb-8 border-b pb-4">
                    <button type="button" onClick={() => onRemoveInfo(idx)} className="absolute top-0 right-0 p-1 text-red-600 hover:bg-red-50 rounded">
                        <Trash2 className="h-4 w-4" />
                    </button>
                    <Label htmlFor={`info-title-${idx}`}>Title</Label>
                    <Input id={`info-title-${idx}`} value={info.title} onChange={e => onUpdateInfo(idx, "title", e.target.value)} className="mt-1" />
                    <Label>Details</Label>
                    <RichTextEditor value={info.body} onChange={data => onUpdateInfo(idx, "body", data)} />
                    <Label htmlFor={`info-note-${idx}`}>Note</Label>
                    <Input id={`info-note-${idx}`} value={info.note} onChange={e => onUpdateInfo(idx, "note", e.target.value)} className="mt-1" placeholder="Add any extra notes here" />
                </div>
            ))}
        </>
    )
}