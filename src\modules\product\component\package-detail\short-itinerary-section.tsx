'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'

interface ShortItinerarySectionProps {
    title: string
    onTitleChange: (value: string) => void
    items: string[]
    onAddItem: () => void
    onUpdateItem: (index: number, text: string) => void
    onRemoveItem: (index: number) => void
}

export function ShortItinerarySection({ 
    title, 
    onTitleChange, 
    items, 
    onAddItem, 
    onUpdateItem, 
    onRemoveItem 
}: ShortItinerarySectionProps) {
    return (
        <>
            <Label htmlFor="itinerary-title">Itinerary Section Title</Label>
            <Input
                id="itinerary-title"
                value={title}
                onChange={e => onTitleChange(e.target.value)}
                className="mt-1 mb-4"
            />
            
            <div className="flex justify-end mb-4">
                <Button variant="outline" size="sm" onClick={onAddItem}>
                    + Add Itinerary Point
                </Button>
            </div>
            
            <div className="space-y-6">
                {items.map((item, idx) => (
                    <div key={idx} className="relative">
                        <Label htmlFor={`itinerary-point-${idx}`}>Point #{idx + 1}</Label>
                        <div className="flex items-center space-x-2 mt-1">
                            <Input
                                id={`itinerary-point-${idx}`}
                                value={item}
                                onChange={e => onUpdateItem(idx, e.target.value)}
                                placeholder="Enter itinerary point..."
                            />
                            {/* Show remove button only if there is more than one item */}
                            {items.length > 1 && (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onRemoveItem(idx)}
                                    className="text-red-600 hover:bg-red-50 hover:text-red-600 shrink-0"
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </>
    )
}